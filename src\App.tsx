
import React, { useEffect, useState } from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './components/pages/Home';
import Library from './components/pages/Library';
import Editor from './components/pages/Editor';
import Discovery from './components/pages/Discovery';
import Reader from './components/pages/Reader';
import ProjectOrion from './components/pages/ProjectOrion';
import AdvancedEditorDemo from './components/pages/AdvancedEditorDemo';
import { BooksProvider } from './hooks/useBooks';
import { LanguageProvider, useLanguage } from './i18n';
import './index.css';

const AppContent = () => {
  const { language } = useLanguage();
  const [isLanguageSwitching, setIsLanguageSwitching] = useState(false);

  useEffect(() => {
    setIsLanguageSwitching(true);
    document.body.style.fontFamily = language === 'ar' ? "'Cairo', sans-serif" : "'Inter', sans-serif";

    const timer = setTimeout(() => {
      setIsLanguageSwitching(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [language]);

  return (
      <HashRouter>
        <div className={`min-h-screen bg-gray-50 text-gray-800 dark:bg-gray-900 dark:text-gray-200 page-transition flex flex-col ${isLanguageSwitching ? 'language-switching' : ''}`}>
          <Header />
          <main className="flex-grow p-4 sm:p-6 lg:p-8">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/library" element={<Library />} />
              <Route path="/editor/:bookId" element={<Editor />} />
              <Route path="/discovery" element={<Discovery />} />
              <Route path="/read/:bookId" element={<Reader />} />
              <Route path="/project-orion" element={<ProjectOrion />} />
          <Route path="/advanced-editor" element={<AdvancedEditorDemo />} />
            </Routes>
          </main>
          <Footer />
        </div>
      </HashRouter>
  );
}


const App = () => {
  return (
    <LanguageProvider>
      <BooksProvider>
        <AppContent />
      </BooksProvider>
    </LanguageProvider>
  );
};

export default App;