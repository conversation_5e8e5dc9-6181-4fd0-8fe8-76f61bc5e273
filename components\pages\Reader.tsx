
import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { useBooks } from '../../hooks/useBooks';
import { Book } from '../../types';
import { useLanguage } from '../../i18n';

const Reader = () => {
    const { bookId } = useParams<{ bookId: string }>();
    const navigate = useNavigate();
    const { getBookById } = useBooks();
    const { t, dir } = useLanguage();
    const [book, setBook] = useState<Book | null>(null);

    useEffect(() => {
        if (bookId) {
            const foundBook = getBookById(bookId);
            if (foundBook) {
                setBook(foundBook);
            } else {
                // if book not found (e.g. not published), redirect
                navigate('/discovery');
            }
        }
    }, [bookId, getBookById, navigate]);

    if (!book) {
        return <div className="text-center p-10">{t('readerLoading')}</div>;
    }

    const handleDownload = () => {
        const element = document.createElement("a");
        const file = new Blob([`
            <!DOCTYPE html>
            <html dir="${dir}">
                <head>
                  <meta charset="UTF-8">
                  <title>${book.title}</title>
                  <style>body { font-family: sans-serif; }</style>
                </head>
                <body>
                    <h1>${book.title}</h1>
                    <h2>${t('by')} ${book.author}</h2>
                    <hr/>
                    ${book.content}
                </body>
            </html>
        `], {type: 'text/html;charset=UTF-8'});
        element.href = URL.createObjectURL(file);
        element.download = `${book.title.replace(/\s/g, '_')}.html`;
        document.body.appendChild(element); 
        element.click();
        document.body.removeChild(element);
    }

    return (
        <div className="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 md:p-12">
            <div className="mb-8 pb-4 border-b border-gray-200 dark:border-gray-700">
                <Link to="/discovery" className="text-blue-500 hover:underline mb-4 block">
                    <i className={`fas ${dir === 'rtl' ? 'fa-arrow-right' : 'fa-arrow-left'} ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>{t('backToDiscovery')}
                </Link>
                <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white">{book.title}</h1>
                <p className="text-lg text-gray-600 dark:text-gray-400 mt-2">{t('by')} {book.author}</p>
                <div className="mt-4 flex items-center justify-between">
                    <span className="text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 font-medium me-2 px-2.5 py-0.5 rounded-full">{t(book.category)}</span>
                    <button onClick={handleDownload} className="text-gray-500 hover:text-blue-500 dark:text-gray-400 dark:hover:text-blue-400 transition">
                       <i className={`fas fa-download ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i> {t('download')}
                    </button>
                </div>
            </div>
            
            <div 
                className="prose dark:prose-invert max-w-none text-gray-800 dark:text-gray-200"
                dangerouslySetInnerHTML={{ __html: book.content }}
                dir={dir}
            />
        </div>
    );
};

export default Reader;