import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Book } from '../types';

type NewBookData = Omit<Book, 'id' | 'createdAt' | 'updatedAt' | 'content' | 'isPublished' | 'price' | 'wordCount'>;

interface BooksContextType {
  books: Book[];
  addBook: (bookData: NewBookData) => Book;
  updateBook: (bookId: string, updates: Partial<Book>) => void;
  deleteBook: (bookId: string) => void;
  getBookById: (bookId: string) => Book | undefined;
  loading: boolean;
}

const BooksContext = createContext<BooksContextType | undefined>(undefined);

export const BooksProvider = ({ children }: { children: React.ReactNode }) => {
  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    try {
      const storedBooks = localStorage.getItem('biomed_books');
      if (storedBooks) {
        setBooks(JSON.parse(storedBooks));
      }
    } catch (error) {
      console.error("Failed to load books from local storage", error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if(!loading) {
       try {
        localStorage.setItem('biomed_books', JSON.stringify(books));
       } catch (error) {
        console.error("Failed to save books to local storage", error);
       }
    }
  }, [books, loading]);

  const addBook = useCallback((bookData: NewBookData): Book => {
    const newBook: Book = {
      ...bookData,
      id: `book-${Date.now()}`,
      content: '',
      isPublished: false,
      price: 'free',
      wordCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setBooks(prevBooks => [...prevBooks, newBook]);
    return newBook;
  }, []);

  const updateBook = useCallback((bookId: string, updates: Partial<Book>) => {
    setBooks(prevBooks =>
      prevBooks.map(book =>
        book.id === bookId ? { ...book, ...updates, updatedAt: new Date().toISOString() } : book
      )
    );
  }, []);

  const deleteBook = useCallback((bookId: string) => {
    setBooks(prevBooks => prevBooks.filter(book => book.id !== bookId));
  }, []);
  
  const getBookById = useCallback((bookId: string): Book | undefined => {
    return books.find(book => book.id === bookId);
  }, [books]);

  return React.createElement(
    BooksContext.Provider,
    { value: { books, addBook, updateBook, deleteBook, getBookById, loading } },
    children
  );
};

export const useBooks = (): BooksContextType => {
  const context = useContext(BooksContext);
  if (context === undefined) {
    throw new Error('useBooks must be used within a BooksProvider');
  }
  return context;
};