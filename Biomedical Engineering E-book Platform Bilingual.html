<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biomedical Engineering E-book Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Use Inter for English and Cairo for Arabic */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }
        html[dir="rtl"] body {
            font-family: 'Cairo', sans-serif;
        }
        .editor-content {
            min-height: 400px;
            border: 1px solid #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            outline: none;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 50;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            border-radius: 0.5rem;
        }
        .nav-link {
            transition: all 0.3s ease;
        }
        .nav-link.active {
            color: #4f46e5;
            border-bottom: 2px solid #4f46e5;
        }
        .nav-link:hover {
            color: #4f46e5;
        }
        [contenteditable]:focus {
            box-shadow: 0 0 0 2px #c7d2fe;
        }
        .lang-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.25rem;
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280; /* gray-500 */
            transition: color 0.2s ease-in-out;
        }
        .lang-btn:hover {
            color: #4f46e5; /* indigo-600 */
        }
        .lang-btn.active {
            color: #4f46e5; /* indigo-600 */
        }
    </style>
</head>
<body class="text-gray-800">

    <div id="app" class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-md sticky top-0 z-40">
            <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <span class="font-bold text-xl text-indigo-600"><i class="fas fa-book-medical mr-2 rtl:ml-2 rtl:mr-0"></i><span data-translate="platform_title">BME E-books</span></span>
                    </div>
                    <div class="hidden md:flex items-center">
                        <div class="ml-10 rtl:ml-0 rtl:mr-10 flex items-baseline space-x-4 rtl:space-x-reverse">
                            <a href="#" class="nav-link px-3 py-2 rounded-md text-sm font-medium" data-page="create" data-translate="nav_create">Create Book</a>
                            <a href="#" class="nav-link px-3 py-2 rounded-md text-sm font-medium" data-page="library" data-translate="nav_library">My Library</a>
                            <a href="#" class="nav-link px-3 py-2 rounded-md text-sm font-medium" data-page="discover" data-translate="nav_discover">Discover</a>
                        </div>
                        <div class="flex items-center ml-6 rtl:ml-0 rtl:mr-6 border-l rtl:border-l-0 rtl:border-r pl-6 rtl:pl-0 rtl:pr-6">
                            <button id="lang-en" class="lang-btn">EN</button>
                            <span class="mx-1 text-gray-300">|</span>
                            <button id="lang-ar" class="lang-btn">AR</button>
                        </div>
                    </div>
                    <div class="md:hidden flex items-center">
                         <div class="flex items-center mr-4">
                            <button id="lang-en-mobile" class="lang-btn">EN</button>
                            <span class="mx-1 text-gray-300">|</span>
                            <button id="lang-ar-mobile" class="lang-btn">AR</button>
                        </div>
                        <button id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none">
                            <span class="sr-only" data-translate="open_menu">Open main menu</span>
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </nav>
            <div id="mobile-menu" class="md:hidden hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                    <a href="#" class="nav-link block px-3 py-2 rounded-md text-base font-medium" data-page="create" data-translate="nav_create_mobile">Create Book</a>
                    <a href="#" class="nav-link block px-3 py-2 rounded-md text-base font-medium" data-page="library" data-translate="nav_library_mobile">My Library</a>
                    <a href="#" class="nav-link block px-3 py-2 rounded-md text-base font-medium" data-page="discover" data-translate="nav_discover_mobile">Discover</a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Create/Edit Book Page -->
            <div id="page-create" class="page">
                <h1 class="text-3xl font-bold mb-6" id="create-edit-title" data-translate="create_new_book">Create a New Book</h1>
                <form id="book-form" class="space-y-6">
                    <input type="hidden" id="book-id">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700" data-translate="form_title">Title</label>
                        <input type="text" id="title" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
                    </div>
                    <div>
                        <label for="author" class="block text-sm font-medium text-gray-700" data-translate="form_author">Author</label>
                        <input type="text" id="author" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700" data-translate="form_category">Category</label>
                        <select id="category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
                            <!-- Options will be populated by JS -->
                        </select>
                    </div>
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700" data-translate="form_description">Description</label>
                        <textarea id="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required></textarea>
                    </div>
                    <div>
                        <label for="keywords" class="block text-sm font-medium text-gray-700" data-translate="form_keywords">Keywords (comma-separated)</label>
                        <input type="text" id="keywords" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>

                    <!-- Advanced Text Editor -->
                    <div class="bg-white p-4 rounded-lg shadow">
                        <h2 class="text-xl font-semibold mb-4" data-translate="editor_content">Book Content</h2>
                        <div class="flex items-center space-x-2 rtl:space-x-reverse mb-2 border-b pb-2">
                            <button type="button" class="editor-btn" data-command="bold" title="Bold"><i class="fas fa-bold"></i></button>
                            <button type="button" class="editor-btn" data-command="italic" title="Italic"><i class="fas fa-italic"></i></button>
                            <button type="button" class="editor-btn" data-command="underline" title="Underline"><i class="fas fa-underline"></i></button>
                            <button type="button" class="editor-btn" data-command="insertHorizontalRule" title="Break"><i class="fas fa-minus"></i> <span data-translate="editor_break">Break</span></button>
                            <button type="button" class="editor-btn" data-command="formatBlock" data-value="blockquote" title="Quote"><i class="fas fa-quote-left"></i> <span data-translate="editor_quote">Quote</span></button>
                        </div>
                        <div id="editor" class="editor-content" contenteditable="true"></div>
                        <div class="text-right rtl:text-left mt-2 text-sm text-gray-500">
                            <span id="word-count">0</span> <span data-translate="editor_words">words</span> | <span id="save-status" class="text-green-600"></span>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4 rtl:space-x-reverse">
                        <button type="button" id="save-book-btn" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" data-translate="btn_save_book">
                            Save Book
                        </button>
                    </div>
                </form>
            </div>

            <!-- My Library Page -->
            <div id="page-library" class="page hidden">
                <h1 class="text-3xl font-bold mb-6" data-translate="library_title">My Library</h1>
                <div class="bg-white p-6 rounded-lg shadow-md mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                        <h3 class="text-lg font-medium text-gray-500" data-translate="stats_total_books">Total Books</h3>
                        <p id="stats-total-books" class="text-3xl font-bold text-indigo-600">0</p>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-500" data-translate="stats_total_words">Total Words</h3>
                        <p id="stats-total-words" class="text-3xl font-bold text-indigo-600">0</p>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-500" data-translate="stats_readership">Total Readership</h3>
                        <p id="stats-readership" class="text-3xl font-bold text-indigo-600">0</p>
                    </div>
                </div>
                <div id="library-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Book cards will be inserted here -->
                </div>
            </div>

            <!-- Discover Page -->
            <div id="page-discover" class="page hidden">
                <h1 class="text-3xl font-bold mb-6" data-translate="discover_title">Discover Books</h1>
                <div class="mb-6 flex flex-col md:flex-row gap-4">
                    <input type="text" id="search-input" class="flex-grow rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    <select id="category-filter" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        <!-- Options will be populated by JS -->
                    </select>
                </div>
                <div id="discover-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Published book cards will be inserted here -->
                </div>
            </div>

            <!-- Book Reader Modal -->
            <div id="reader-modal" class="modal">
                <div class="modal-content">
                    <div class="flex justify-between items-center mb-4">
                        <h2 id="reader-title" class="text-2xl font-bold"></h2>
                        <button id="close-reader" class="text-gray-500 hover:text-gray-800 text-3xl leading-none">&times;</button>
                    </div>
                    <div id="reader-content" class="prose max-w-none"></div>
                    <div class="mt-4 flex justify-end">
                         <button id="download-book-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700" data-translate="btn_download">
                            <i class="fas fa-download mr-2 rtl:ml-2 rtl:mr-0"></i>Download
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Publishing Modal -->
            <div id="publish-modal" class="modal">
                <div class="modal-content">
                    <h2 class="text-2xl font-bold mb-4" data-translate="publish_title">Publish Book</h2>
                    <form id="publish-form">
                        <input type="hidden" id="publish-book-id">
                        <div>
                            <label class="block text-sm font-medium text-gray-700" data-translate="publish_visibility">Visibility</label>
                            <select id="publish-visibility" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                <option value="public" data-translate="visibility_public">Public</option>
                                <option value="private" data-translate="visibility_private">Private</option>
                            </select>
                        </div>
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700" data-translate="publish_pricing">Pricing</label>
                            <select id="publish-pricing" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                                <option value="free" data-translate="pricing_free">Free</option>
                                <option value="paid" data-translate="pricing_paid">Paid</option>
                            </select>
                        </div>
                        <div class="mt-6 flex justify-end space-x-3 rtl:space-x-reverse">
                            <button type="button" id="cancel-publish-btn" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50" data-translate="btn_cancel">Cancel</button>
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700" data-translate="btn_publish">Publish</button>
                        </div>
                    </form>
                </div>
            </div>

        </main>

        <!-- Footer -->
        <footer class="bg-white border-t mt-auto">
            <div class="container mx-auto py-4 px-4 sm:px-6 lg:px-8 text-center text-sm text-gray-500">
                <p><span data-translate="footer_author">Author</span>: Dr. Mohammed Yagoub Esmail, SUST - BME, @ 2025</p>
                <p><span data-translate="footer_copyright">Copyright</span> &copy; <a href="mailto:<EMAIL>" class="hover:text-indigo-600"><EMAIL></a></p>
                <p><span data-translate="footer_phone">Phone</span>: +249912867327, +966538076790</p>
            </div>
        </footer>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const translations = {
            en: {
                platform_title: "BME E-books",
                nav_create: "Create Book",
                nav_library: "My Library",
                nav_discover: "Discover",
                nav_create_mobile: "Create Book",
                nav_library_mobile: "My Library",
                nav_discover_mobile: "Discover",
                open_menu: "Open main menu",
                create_new_book: "Create a New Book",
                edit_book: "Edit Book",
                form_title: "Title",
                form_author: "Author",
                form_category: "Category",
                form_description: "Description",
                form_keywords: "Keywords (comma-separated)",
                editor_content: "Book Content",
                editor_break: "Break",
                editor_quote: "Quote",
                editor_words: "words",
                btn_save_book: "Save Book",
                library_title: "My Library",
                stats_total_books: "Total Books",
                stats_total_words: "Total Words",
                stats_readership: "Total Readership",
                discover_title: "Discover Books",
                search_placeholder: "Search by title, author, or keywords...",
                category_all: "All Categories",
                categories: {
                    "Biological Sciences": "Biological Sciences",
                    "Fundamental Engineering Sciences": "Fundamental Engineering Sciences",
                    "Electronics": "Electronics",
                    "Medical Instrumentation and Devices": "Medical Instrumentation and Devices",
                    "Sensors and Transducers": "Sensors and Transducers",
                    "Other": "Other"
                },
                btn_download: "Download",
                publish_title: "Publish Book",
                publish_visibility: "Visibility",
                visibility_public: "Public",
                visibility_private: "Private",
                publish_pricing: "Pricing",
                pricing_free: "Free",
                pricing_paid: "Paid",
                btn_cancel: "Cancel",
                btn_publish: "Publish",
                footer_author: "Author",
                footer_copyright: "Copyright",
                footer_phone: "Phone",
                status_saved: "Book saved successfully!",
                status_autosaved: "Auto-saved!",
                status_draft_autosaved: "Draft auto-saved!",
                confirm_delete: "Are you sure you want to delete this book? This action cannot be undone.",
                confirm_load_draft: "You have an unsaved draft. Do you want to load it?",
                library_empty: "You haven't created any books yet. Go to the \"Create Book\" page to get started!",
                discover_empty: "No books found matching your criteria.",
                status_draft: "Draft",
                status_public: "Public",
                status_private: "Private",
                btn_read_book: "Read Book",
                by_author: "By",
            },
            ar: {
                platform_title: "كتب الهندسة الطبية",
                nav_create: "إنشاء كتاب",
                nav_library: "مكتبتي",
                nav_discover: "اكتشف",
                nav_create_mobile: "إنشاء كتاب",
                nav_library_mobile: "مكتبتي",
                nav_discover_mobile: "اكتشف",
                open_menu: "فتح القائمة الرئيسية",
                create_new_book: "إنشاء كتاب جديد",
                edit_book: "تعديل الكتاب",
                form_title: "العنوان",
                form_author: "المؤلف",
                form_category: "الفئة",
                form_description: "الوصف",
                form_keywords: "الكلمات المفتاحية (مفصولة بفاصلة)",
                editor_content: "محتوى الكتاب",
                editor_break: "فاصل",
                editor_quote: "اقتباس",
                editor_words: "كلمة",
                btn_save_book: "حفظ الكتاب",
                library_title: "مكتبتي",
                stats_total_books: "إجمالي الكتب",
                stats_total_words: "إجمالي الكلمات",
                stats_readership: "إجمالي القراء",
                discover_title: "اكتشف الكتب",
                search_placeholder: "ابحث بالعنوان، المؤلف، أو الكلمات المفتاحية...",
                category_all: "كل الفئات",
                categories: {
                    "Biological Sciences": "العلوم البيولوجية",
                    "Fundamental Engineering Sciences": "علوم الهندسة الأساسية",
                    "Electronics": "الإلكترونيات",
                    "Medical Instrumentation and Devices": "الأجهزة والمعدات الطبية",
                    "Sensors and Transducers": "المستشعرات ومحولات الطاقة",
                    "Other": "أخرى"
                },
                btn_download: "تحميل",
                publish_title: "نشر كتاب",
                publish_visibility: "مستوى العرض",
                visibility_public: "عام",
                visibility_private: "خاص",
                publish_pricing: "السعر",
                pricing_free: "مجاني",
                pricing_paid: "مدفوع",
                btn_cancel: "إلغاء",
                btn_publish: "نشر",
                footer_author: "المؤلف",
                footer_copyright: "حقوق النشر",
                footer_phone: "هاتف",
                status_saved: "تم حفظ الكتاب بنجاح!",
                status_autosaved: "تم الحفظ التلقائي!",
                status_draft_autosaved: "تم حفظ المسودة تلقائيًا!",
                confirm_delete: "هل أنت متأكد من رغبتك في حذف هذا الكتاب؟ لا يمكن التراجع عن هذا الإجراء.",
                confirm_load_draft: "لديك مسودة غير محفوظة. هل تريد تحميلها؟",
                library_empty: "لم تقم بإنشاء أي كتب بعد. اذهب إلى صفحة \"إنشاء كتاب\" للبدء!",
                discover_empty: "لم يتم العثور على كتب تطابق معايير البحث.",
                status_draft: "مسودة",
                status_public: "عام",
                status_private: "خاص",
                btn_read_book: "اقرأ الكتاب",
                by_author: "بواسطة",
            }
        };

        const app = {
            // State
            books: [],
            currentPage: 'create',
            editingBookId: null,
            autoSaveInterval: null,
            currentLanguage: 'en',

            // DOM Elements
            elements: {
                html: document.documentElement,
                pages: document.querySelectorAll('.page'),
                navLinks: document.querySelectorAll('.nav-link'),
                mobileMenu: document.getElementById('mobile-menu'),
                mobileMenuButton: document.getElementById('mobile-menu-button'),
                langEnBtn: document.getElementById('lang-en'),
                langArBtn: document.getElementById('lang-ar'),
                langEnMobileBtn: document.getElementById('lang-en-mobile'),
                langArMobileBtn: document.getElementById('lang-ar-mobile'),
                createEditTitle: document.getElementById('create-edit-title'),
                bookForm: document.getElementById('book-form'),
                bookIdInput: document.getElementById('book-id'),
                titleInput: document.getElementById('title'),
                authorInput: document.getElementById('author'),
                categoryInput: document.getElementById('category'),
                descriptionInput: document.getElementById('description'),
                keywordsInput: document.getElementById('keywords'),
                editor: document.getElementById('editor'),
                wordCount: document.getElementById('word-count'),
                saveStatus: document.getElementById('save-status'),
                saveBookBtn: document.getElementById('save-book-btn'),
                libraryList: document.getElementById('library-list'),
                statsTotalBooks: document.getElementById('stats-total-books'),
                statsTotalWords: document.getElementById('stats-total-words'),
                statsReadership: document.getElementById('stats-readership'),
                discoverList: document.getElementById('discover-list'),
                searchInput: document.getElementById('search-input'),
                categoryFilter: document.getElementById('category-filter'),
                readerModal: document.getElementById('reader-modal'),
                readerTitle: document.getElementById('reader-title'),
                readerContent: document.getElementById('reader-content'),
                closeReaderBtn: document.getElementById('close-reader'),
                downloadBookBtn: document.getElementById('download-book-btn'),
                publishModal: document.getElementById('publish-modal'),
                publishForm: document.getElementById('publish-form'),
                publishBookIdInput: document.getElementById('publish-book-id'),
                cancelPublishBtn: document.getElementById('cancel-publish-btn'),
            },

            // Initialization
            init() {
                const savedLang = localStorage.getItem('bme_ebook_lang') || 'en';
                this.setLanguage(savedLang);
                this.loadBooks();
                this.setupEventListeners();
                this.navigateTo('create');
                this.loadDraft();
            },
            
            // Language
            setLanguage(lang) {
                this.currentLanguage = lang;
                localStorage.setItem('bme_ebook_lang', lang);
                
                this.elements.html.lang = lang;
                this.elements.html.dir = lang === 'ar' ? 'rtl' : 'ltr';

                [this.elements.langEnBtn, this.elements.langEnMobileBtn].forEach(btn => btn.classList.toggle('active', lang === 'en'));
                [this.elements.langArBtn, this.elements.langArMobileBtn].forEach(btn => btn.classList.toggle('active', lang === 'ar'));
                
                this.updateUIText();
            },

            updateUIText() {
                const langTranslations = translations[this.currentLanguage];
                document.querySelectorAll('[data-translate]').forEach(el => {
                    const key = el.dataset.translate;
                    if (langTranslations[key]) {
                        el.textContent = langTranslations[key];
                    }
                });

                // Update placeholders
                this.elements.searchInput.placeholder = langTranslations.search_placeholder;

                // Update category dropdowns
                this.updateCategoryDropdown(this.elements.categoryInput, false);
                this.updateCategoryDropdown(this.elements.categoryFilter, true);
                
                // Rerender dynamic lists to apply language changes
                if(this.currentPage === 'library') this.renderLibrary();
                if(this.currentPage === 'discover') this.renderDiscoverList();
            },
            
            updateCategoryDropdown(selectElement, includeAll) {
                const langCategories = translations[this.currentLanguage].categories;
                const currentVal = selectElement.value;
                selectElement.innerHTML = '';

                if (includeAll) {
                    const allOption = document.createElement('option');
                    allOption.value = '';
                    allOption.textContent = translations[this.currentLanguage].category_all;
                    selectElement.appendChild(allOption);
                }

                for (const key in translations.en.categories) {
                    const option = document.createElement('option');
                    option.value = key;
                    option.textContent = langCategories[key];
                    selectElement.appendChild(option);
                }
                selectElement.value = currentVal;
            },

            // Event Listeners
            setupEventListeners() {
                this.elements.navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.navigateTo(e.target.dataset.page);
                        this.elements.mobileMenu.classList.add('hidden');
                    });
                });

                this.elements.mobileMenuButton.addEventListener('click', () => {
                    this.elements.mobileMenu.classList.toggle('hidden');
                });
                
                [this.elements.langEnBtn, this.elements.langEnMobileBtn].forEach(btn => btn.addEventListener('click', () => this.setLanguage('en')));
                [this.elements.langArBtn, this.elements.langArMobileBtn].forEach(btn => btn.addEventListener('click', () => this.setLanguage('ar')));

                this.elements.bookForm.addEventListener('submit', (e) => { e.preventDefault(); this.saveBook(); });
                this.elements.saveBookBtn.addEventListener('click', () => this.saveBook());
                this.elements.editor.addEventListener('input', () => this.updateWordCount());
                
                document.querySelectorAll('.editor-btn').forEach(button => {
                    button.addEventListener('click', () => {
                        const command = button.dataset.command;
                        const value = button.dataset.value || null;
                        document.execCommand(command, false, value);
                        this.elements.editor.focus();
                    });
                });

                this.elements.searchInput.addEventListener('input', () => this.renderDiscoverList());
                this.elements.categoryFilter.addEventListener('change', () => this.renderDiscoverList());
                
                this.elements.closeReaderBtn.addEventListener('click', () => this.closeReader());
                this.elements.readerModal.addEventListener('click', (e) => { if (e.target === this.elements.readerModal) this.closeReader(); });
                this.elements.downloadBookBtn.addEventListener('click', () => this.downloadBook());
                this.elements.publishForm.addEventListener('submit', (e) => { e.preventDefault(); this.confirmPublish(); });
                this.elements.cancelPublishBtn.addEventListener('click', () => this.closePublishModal());
                
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey) {
                        switch(e.key.toLowerCase()) {
                            case 's': e.preventDefault(); this.saveBook(); break;
                            case 'b': e.preventDefault(); document.execCommand('bold'); break;
                            case 'i': e.preventDefault(); document.execCommand('italic'); break;
                            case 'u': e.preventDefault(); document.execCommand('underline'); break;
                        }
                    }
                });
            },

            navigateTo(page) {
                this.currentPage = page;
                this.elements.pages.forEach(p => p.classList.add('hidden'));
                document.getElementById(`page-${page}`).classList.remove('hidden');

                this.elements.navLinks.forEach(link => link.classList.toggle('active', link.dataset.page === page));

                if (page === 'create') {
                    const titleKey = this.editingBookId ? 'edit_book' : 'create_new_book';
                    this.elements.createEditTitle.textContent = translations[this.currentLanguage][titleKey];
                    if (!this.editingBookId) this.resetForm();
                } else if (page === 'library') {
                    this.renderLibrary();
                } else if (page === 'discover') {
                    this.renderDiscoverList();
                }
            },

            loadBooks() {
                const storedBooks = localStorage.getItem('bme_ebooks');
                if (storedBooks) this.books = JSON.parse(storedBooks);
            },

            saveBooksToStorage() {
                localStorage.setItem('bme_ebooks', JSON.stringify(this.books));
            },

            saveBook() {
                const id = this.elements.bookIdInput.value || `book-${Date.now()}`;
                const bookIndex = this.books.findIndex(b => b.id === id);

                const bookData = {
                    id: id,
                    title: this.elements.titleInput.value,
                    author: this.elements.authorInput.value,
                    category: this.elements.categoryInput.value,
                    description: this.elements.descriptionInput.value,
                    keywords: this.elements.keywordsInput.value.split(',').map(k => k.trim()),
                    content: this.elements.editor.innerHTML,
                    wordCount: this.getWordCount(this.elements.editor.innerText),
                    lastModified: new Date().toISOString(),
                    published: bookIndex !== -1 ? this.books[bookIndex].published : false,
                    publishSettings: bookIndex !== -1 ? this.books[bookIndex].publishSettings : { visibility: 'private', pricing: 'free' }
                };

                if (bookIndex !== -1) this.books[bookIndex] = bookData;
                else this.books.push(bookData);

                this.saveBooksToStorage();
                this.showSaveStatus(translations[this.currentLanguage].status_saved);
                this.editingBookId = null;
                this.navigateTo('library');
            },

            editBook(id) {
                const book = this.books.find(b => b.id === id);
                if (book) {
                    this.editingBookId = id;
                    this.elements.bookIdInput.value = book.id;
                    this.elements.titleInput.value = book.title;
                    this.elements.authorInput.value = book.author;
                    this.elements.categoryInput.value = book.category;
                    this.elements.descriptionInput.value = book.description;
                    this.elements.keywordsInput.value = book.keywords.join(', ');
                    this.elements.editor.innerHTML = book.content;
                    this.updateWordCount();
                    this.navigateTo('create');
                    this.startAutoSave();
                }
            },

            deleteBook(id) {
                if (confirm(translations[this.currentLanguage].confirm_delete)) {
                    this.books = this.books.filter(b => b.id !== id);
                    this.saveBooksToStorage();
                    this.renderLibrary();
                }
            },

            resetForm() {
                this.elements.bookForm.reset();
                this.elements.bookIdInput.value = '';
                this.elements.editor.innerHTML = '';
                this.updateWordCount();
                this.editingBookId = null;
                this.startAutoSave();
            },

            updateWordCount() {
                const wordCount = this.getWordCount(this.elements.editor.innerText);
                this.elements.wordCount.textContent = wordCount;
            },

            getWordCount(text) {
                return text.trim().split(/\s+/).filter(word => word.length > 0).length;
            },
            
            startAutoSave() {
                if(this.autoSaveInterval) clearInterval(this.autoSaveInterval);
                this.autoSaveInterval = setInterval(() => {
                    const content = this.elements.editor.innerHTML;
                    if(this.editingBookId) {
                        const bookIndex = this.books.findIndex(b => b.id === this.editingBookId);
                        if(bookIndex !== -1) {
                            this.books[bookIndex].content = content;
                            this.books[bookIndex].wordCount = this.getWordCount(this.elements.editor.innerText);
                            this.saveBooksToStorage();
                            this.showSaveStatus(translations[this.currentLanguage].status_autosaved);
                        }
                    } else {
                        const draft = {
                            title: this.elements.titleInput.value,
                            author: this.elements.authorInput.value,
                            category: this.elements.categoryInput.value,
                            description: this.elements.descriptionInput.value,
                            keywords: this.elements.keywordsInput.value,
                            content: content
                        };
                        localStorage.setItem('bme_ebook_draft', JSON.stringify(draft));
                        this.showSaveStatus(translations[this.currentLanguage].status_draft_autosaved);
                    }
                }, 5000);
            },
            
            loadDraft() {
                const draft = localStorage.getItem('bme_ebook_draft');
                if (draft && confirm(translations[this.currentLanguage].confirm_load_draft)) {
                    const parsedDraft = JSON.parse(draft);
                    this.elements.titleInput.value = parsedDraft.title || '';
                    this.elements.authorInput.value = parsedDraft.author || '';
                    this.elements.categoryInput.value = parsedDraft.category || '';
                    this.elements.descriptionInput.value = parsedDraft.description || '';
                    this.elements.keywordsInput.value = parsedDraft.keywords || '';
                    this.elements.editor.innerHTML = parsedDraft.content || '';
                    this.updateWordCount();
                }
            },

            showSaveStatus(message) {
                this.elements.saveStatus.textContent = message;
                setTimeout(() => { this.elements.saveStatus.textContent = ''; }, 3000);
            },

            renderLibrary() {
                this.elements.libraryList.innerHTML = '';
                if (this.books.length === 0) {
                    this.elements.libraryList.innerHTML = `<p class="text-gray-500 col-span-full">${translations[this.currentLanguage].library_empty}</p>`;
                } else {
                    this.books.forEach(book => this.elements.libraryList.appendChild(this.createBookCard(book, 'library')));
                }
                this.updateLibraryStats();
            },

            updateLibraryStats() {
                this.elements.statsTotalBooks.textContent = this.books.length;
                const totalWords = this.books.reduce((sum, book) => sum + (book.wordCount || 0), 0);
                this.elements.statsTotalWords.textContent = totalWords.toLocaleString(this.currentLanguage);
                const readership = this.books.reduce((sum, book) => sum + (book.reads || 0), 0);
                this.elements.statsReadership.textContent = readership.toLocaleString(this.currentLanguage);
            },

            renderDiscoverList() {
                this.elements.discoverList.innerHTML = '';
                const searchTerm = this.elements.searchInput.value.toLowerCase();
                const category = this.elements.categoryFilter.value;

                const publishedBooks = this.books.filter(book => book.published && book.publishSettings.visibility === 'public');
                const filteredBooks = publishedBooks.filter(book => {
                    const matchesCategory = !category || book.category === category;
                    const matchesSearch = !searchTerm ||
                        book.title.toLowerCase().includes(searchTerm) ||
                        book.author.toLowerCase().includes(searchTerm) ||
                        book.keywords.some(k => k.toLowerCase().includes(searchTerm));
                    return matchesCategory && matchesSearch;
                });

                if (filteredBooks.length === 0) {
                    this.elements.discoverList.innerHTML = `<p class="text-gray-500 col-span-full">${translations[this.currentLanguage].discover_empty}</p>`;
                } else {
                    filteredBooks.forEach(book => this.elements.discoverList.appendChild(this.createBookCard(book, 'discover')));
                }
            },
            
            createBookCard(book, context) {
                const card = document.createElement('div');
                card.className = 'bg-white rounded-lg shadow-md p-6 flex flex-col text-left rtl:text-right';
                const langTrans = translations[this.currentLanguage];
                
                let statusBadge = '';
                if (context === 'library') {
                    if (book.published) {
                        const visibilityKey = `status_${book.publishSettings.visibility}`;
                        const colorClass = book.publishSettings.visibility === 'public' ? 'text-green-600 bg-green-200' : 'text-yellow-600 bg-yellow-200';
                        statusBadge = `<span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full ${colorClass}">${langTrans[visibilityKey]}</span>`;
                    } else {
                        statusBadge = `<span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-gray-600 bg-gray-200">${langTrans.status_draft}</span>`;
                    }
                }
                
                const categoryName = langTrans.categories[book.category] || book.category;

                card.innerHTML = `
                    <div class="flex-grow">
                        <div class="flex justify-between items-start">
                            <h3 class="text-xl font-bold mb-2 text-indigo-700">${book.title}</h3>
                            ${statusBadge}
                        </div>
                        <p class="text-sm font-medium text-gray-600 mb-1">${langTrans.by_author} ${book.author}</p>
                        <p class="text-sm text-gray-500 mb-3">${categoryName}</p>
                        <p class="text-gray-700 text-sm mb-4 h-16 overflow-hidden">${book.description}</p>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200 flex ${context === 'library' ? 'justify-between' : 'justify-end'} items-center">
                        ${context === 'library' ? `
                        <div class="flex space-x-2 rtl:space-x-reverse">
                            <button class="edit-btn text-blue-600 hover:text-blue-800" title="Edit"><i class="fas fa-edit"></i></button>
                            <button class="delete-btn text-red-600 hover:text-red-800" title="Delete"><i class="fas fa-trash"></i></button>
                            <button class="publish-btn text-green-600 hover:text-green-800" title="Publish"><i class="fas fa-upload"></i></button>
                        </div>
                        ` : ''}
                        <button class="read-btn bg-indigo-100 text-indigo-700 hover:bg-indigo-200 text-sm font-semibold py-2 px-4 rounded-full">${langTrans.btn_read_book}</button>
                    </div>
                `;
                
                if (context === 'library') {
                    card.querySelector('.edit-btn').addEventListener('click', () => this.editBook(book.id));
                    card.querySelector('.delete-btn').addEventListener('click', () => this.deleteBook(book.id));
                    card.querySelector('.publish-btn').addEventListener('click', () => this.openPublishModal(book.id));
                }
                card.querySelector('.read-btn').addEventListener('click', () => this.openReader(book.id));

                return card;
            },
            
            openPublishModal(id) {
                const book = this.books.find(b => b.id === id);
                if (book) {
                    this.elements.publishBookIdInput.value = id;
                    document.getElementById('publish-visibility').value = book.publishSettings.visibility;
                    document.getElementById('publish-pricing').value = book.publishSettings.pricing;
                    this.elements.publishModal.style.display = 'block';
                }
            },

            closePublishModal() {
                this.elements.publishModal.style.display = 'none';
            },

            confirmPublish() {
                const id = this.elements.publishBookIdInput.value;
                const bookIndex = this.books.findIndex(b => b.id === id);
                if (bookIndex !== -1) {
                    this.books[bookIndex].published = true;
                    this.books[bookIndex].publishSettings = {
                        visibility: document.getElementById('publish-visibility').value,
                        pricing: document.getElementById('publish-pricing').value
                    };
                    this.saveBooksToStorage();
                    this.closePublishModal();
                    this.renderLibrary();
                }
            },

            openReader(id) {
                const book = this.books.find(b => b.id === id);
                if (book) {
                    this.elements.readerTitle.textContent = book.title;
                    this.elements.readerContent.innerHTML = book.content;
                    this.elements.downloadBookBtn.dataset.bookId = id;
                    this.elements.readerModal.style.display = 'block';
                    book.reads = (book.reads || 0) + 1;
                    this.saveBooksToStorage();
                }
            },

            closeReader() {
                this.elements.readerModal.style.display = 'none';
                if (this.currentPage === 'library') this.renderLibrary();
            },

            downloadBook() {
                const bookId = this.elements.downloadBookBtn.dataset.bookId;
                const book = this.books.find(b => b.id === bookId);
                if (book) {
                    const content = `<!DOCTYPE html><html lang="${this.currentLanguage}" dir="${this.currentLanguage === 'ar' ? 'rtl' : 'ltr'}"><head><meta charset="UTF-8"><title>${book.title}</title></head><body><h1>${book.title}</h1><h2>${translations[this.currentLanguage].by_author} ${book.author}</h2><hr>${book.content}</body></html>`;
                    const blob = new Blob([content], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${book.title.replace(/\s+/g, '_')}.html`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }
            }
        };

        app.init();
    });
    </script>
</body>
</html>
