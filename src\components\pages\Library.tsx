
import React, { useState, useMemo } from 'react';
import { useBooks } from '../../hooks/useBooks';
import { Book, BookCategory } from '../../types';
import { useNavigate } from 'react-router-dom';
import Modal from '../Modal';
import { useLanguage } from '../../i18n';
import FileUpload from '../FileUpload';
import PDFExport from '../PDFExport';

interface BookCardProps {
    book: Book;
    onEdit: (id: string) => void;
    onDelete: (id: string) => void;
    onRead: (id: string) => void;
    onExportPDF: (book: Book) => void;
    viewMode?: 'grid' | 'list';
}

const BookCard = ({ book, onEdit, onDelete, onRead, onExportPDF, viewMode = 'grid' }: BookCardProps) => {
    const { t } = useLanguage();

    if (viewMode === 'list') {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-6 flex items-center gap-6">
                <div className="flex-shrink-0">
                    <div className="w-16 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i className="fas fa-book text-white text-2xl"></i>
                    </div>
                </div>
                <div className="flex-grow">
                    <div className="flex items-start justify-between">
                        <div className="flex-grow">
                            <h3 className="text-xl font-bold text-blue-600 dark:text-blue-400 mb-1">{book.title}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{t('by')} {book.author}</p>
                            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400 mb-2">
                                <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">{t(book.category)}</span>
                                <span>{book.wordCount.toLocaleString()} {t('words')}</span>
                                <span className={`px-2 py-1 rounded ${book.isPublished ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'}`}>
                                    {book.isPublished ? t('publishedStatus') : t('draft')}
                                </span>
                                <span>{new Date(book.updatedAt).toLocaleDateString('ar-SA')}</span>
                            </div>
                            <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">{book.description}</p>
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                            <button
                                onClick={() => onRead(book.id)}
                                className="bg-green-500 hover:bg-green-600 text-white p-2 rounded-lg transition-colors"
                                title={t('read')}
                            >
                                <i className="fas fa-book-open"></i>
                            </button>
                            <button
                                onClick={() => onEdit(book.id)}
                                className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-lg transition-colors"
                                title={t('edit')}
                            >
                                <i className="fas fa-pencil-alt"></i>
                            </button>
                            <button
                                onClick={() => onDelete(book.id)}
                                className="bg-red-500 hover:bg-red-600 text-white p-2 rounded-lg transition-colors"
                                title={t('delete')}
                            >
                                <i className="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-4 flex flex-col justify-between transform hover:scale-105">
            <div>
                <div className="flex items-center gap-3 mb-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="fas fa-book text-white"></i>
                    </div>
                    <div className="flex-grow min-w-0">
                        <h3 className="text-lg font-bold text-blue-600 dark:text-blue-400 truncate">{book.title}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{t('by')} {book.author}</p>
                    </div>
                </div>
                <div className="flex items-center gap-2 mb-2">
                    <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded">{t(book.category)}</span>
                    <span className={`text-xs px-2 py-1 rounded ${book.isPublished ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'}`}>
                        {book.isPublished ? t('publishedStatus') : t('draft')}
                    </span>
                </div>
                <p className="text-sm mt-2 text-gray-700 dark:text-gray-300 line-clamp-3">{book.description}</p>
                <div className="text-xs mt-3 text-gray-500 dark:text-gray-400">
                    <span>{book.wordCount.toLocaleString()} {t('words')}</span>
                    <span className="mx-2">•</span>
                    <span>{new Date(book.updatedAt).toLocaleDateString('ar-SA')}</span>
                </div>
            </div>
            <div className="flex justify-end gap-2 mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <button
                    onClick={() => onRead(book.id)}
                    className="text-green-500 hover:text-green-700 dark:hover:text-green-400 p-2 rounded-full transition-colors hover:bg-green-50 dark:hover:bg-green-900"
                    title={t('read')}
                >
                    <i className="fas fa-book-open"></i>
                </button>
                <button
                    onClick={() => onEdit(book.id)}
                    className="text-blue-500 hover:text-blue-700 dark:hover:text-blue-400 p-2 rounded-full transition-colors hover:bg-blue-50 dark:hover:bg-blue-900"
                    title={t('edit')}
                >
                    <i className="fas fa-pencil-alt"></i>
                </button>
                <button
                    onClick={() => onExportPDF(book)}
                    className="text-purple-500 hover:text-purple-700 dark:hover:text-purple-400 p-2 rounded-full transition-colors hover:bg-purple-50 dark:hover:bg-purple-900"
                    title={t('convertToPdf')}
                >
                    <i className="fas fa-file-pdf"></i>
                </button>
                <button
                    onClick={() => onDelete(book.id)}
                    className="text-red-500 hover:text-red-700 dark:hover:text-red-400 p-2 rounded-full transition-colors hover:bg-red-50 dark:hover:bg-red-900"
                    title={t('delete')}
                >
                    <i className="fas fa-trash"></i>
                </button>
            </div>
        </div>
    );
};

const Library = () => {
    const { books, addBook, deleteBook, loading } = useBooks();
    const { t, dir } = useLanguage();
    const navigate = useNavigate();
    const [isCreateModalOpen, setCreateModalOpen] = useState(false);
    const [newBookData, setNewBookData] = useState({ title: '', author: '', description: '', category: BookCategory.OTHER, keywords: '' });
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState<'title' | 'author' | 'date' | 'wordCount'>('date');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
    const [filterBy, setFilterBy] = useState<'all' | 'published' | 'draft'>('all');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [showFileUpload, setShowFileUpload] = useState(false);
    const [showPDFExport, setShowPDFExport] = useState(false);
    const [selectedBookForPDF, setSelectedBookForPDF] = useState<Book | null>(null);
    
    const totalWords = books.reduce((sum, book) => sum + book.wordCount, 0);
    const publishedBooks = books.filter(book => book.isPublished);
    const draftBooks = books.filter(book => !book.isPublished);
    const averageWordsPerBook = books.length > 0 ? Math.round(totalWords / books.length) : 0;

    // Filter and sort books
    const filteredAndSortedBooks = useMemo(() => {
        return books
            .filter(book => {
                // Filter by status
                if (filterBy === 'published' && !book.isPublished) return false;
                if (filterBy === 'draft' && book.isPublished) return false;

                // Filter by search term
                if (searchTerm) {
                    const searchLower = searchTerm.toLowerCase();
                    return book.title.toLowerCase().includes(searchLower) ||
                           book.author.toLowerCase().includes(searchLower) ||
                           book.description.toLowerCase().includes(searchLower) ||
                           book.keywords.some(k => k.toLowerCase().includes(searchLower));
                }

                return true;
            })
            .sort((a, b) => {
                let comparison = 0;
                switch (sortBy) {
                    case 'title':
                        comparison = a.title.localeCompare(b.title);
                        break;
                    case 'author':
                        comparison = a.author.localeCompare(b.author);
                        break;
                    case 'date':
                        comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
                        break;
                    case 'wordCount':
                        comparison = a.wordCount - b.wordCount;
                        break;
                }
                return sortOrder === 'asc' ? comparison : -comparison;
            });
    }, [books, filterBy, searchTerm, sortBy, sortOrder]);

    const handleCreateBook = () => {
        if (newBookData.title && newBookData.author) {
            const newBook = addBook({
                title: newBookData.title,
                author: newBookData.author,
                description: newBookData.description,
                category: newBookData.category,
                keywords: newBookData.keywords.split(',').map(k => k.trim()).filter(Boolean),
            });
            setCreateModalOpen(false);
            setNewBookData({ title: '', author: '', description: '', category: BookCategory.OTHER, keywords: '' });
            navigate(`/editor/${newBook.id}`);
        }
    };
    
    const handleDeleteBook = (id: string) => {
        if(window.confirm(t('deleteConfirmation'))){
            deleteBook(id);
        }
    };

    const handleFileImport = (bookData: Partial<Book>) => {
        const newBook = addBook({
            title: bookData.title || 'Imported Book',
            author: bookData.author || 'Unknown Author',
            description: bookData.description || 'Imported from file',
            category: bookData.category || BookCategory.OTHER,
            keywords: [],
        });

        // Update the book with imported content
        if (bookData.content) {
            // In a real app, you'd update the book content through the books context
            // For now, we'll navigate to the editor with the imported content
            navigate(`/editor/${newBook.id}`, { state: { importedContent: bookData.content } });
        } else {
            navigate(`/editor/${newBook.id}`);
        }
        setShowFileUpload(false);
    };

    const handleExportToPDF = (book: Book) => {
        setSelectedBookForPDF(book);
        setShowPDFExport(true);
    };

    if (loading) {
        return <div className="text-center p-10">{t('loadingLibrary')}</div>;
    }

    return (
        <div className="container mx-auto">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">{t('libraryTitle')}</h2>
                <div className="flex gap-3">
                    <button
                        onClick={() => setShowFileUpload(true)}
                        className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg shadow-lg transition-transform transform hover:scale-105 flex items-center"
                    >
                        <i className={`fas fa-upload ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>{t('importFromFile')}
                    </button>
                    <button
                        onClick={() => setCreateModalOpen(true)}
                        className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg shadow-lg transition-transform transform hover:scale-105 flex items-center"
                    >
                        <i className={`fas fa-plus ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>{t('createNewBook')}
                    </button>
                </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow text-center border-l-4 border-blue-500">
                    <div className="flex items-center justify-center mb-2">
                        <i className="fas fa-book text-blue-500 text-2xl mr-3"></i>
                        <p className="text-4xl font-bold text-blue-500">{books.length}</p>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 font-medium">{t('totalBooks')}</p>
                </div>
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow text-center border-l-4 border-green-500">
                    <div className="flex items-center justify-center mb-2">
                        <i className="fas fa-check-circle text-green-500 text-2xl mr-3"></i>
                        <p className="text-4xl font-bold text-green-500">{publishedBooks.length}</p>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 font-medium">{t('published')}</p>
                </div>
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow text-center border-l-4 border-yellow-500">
                    <div className="flex items-center justify-center mb-2">
                        <i className="fas fa-file-alt text-yellow-500 text-2xl mr-3"></i>
                        <p className="text-4xl font-bold text-yellow-500">{draftBooks.length}</p>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 font-medium">{t('draft')}</p>
                </div>
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow text-center border-l-4 border-purple-500">
                    <div className="flex items-center justify-center mb-2">
                        <i className="fas fa-calculator text-purple-500 text-2xl mr-3"></i>
                        <p className="text-4xl font-bold text-purple-500">{averageWordsPerBook.toLocaleString()}</p>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 font-medium">{t('averageWords')}</p>
                </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow text-center border-l-4 border-indigo-500">
                    <div className="flex items-center justify-center mb-2">
                        <i className="fas fa-font text-indigo-500 text-2xl mr-3"></i>
                        <p className="text-4xl font-bold text-indigo-500">{totalWords.toLocaleString()}</p>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 font-medium">{t('totalWords')}</p>
                </div>
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow text-center border-l-4 border-pink-500">
                    <div className="flex items-center justify-center mb-2">
                        <i className="fas fa-eye text-pink-500 text-2xl mr-3"></i>
                        <p className="text-4xl font-bold text-pink-500">0</p>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 font-medium">{t('totalReadership')}</p>
                </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <div className="flex flex-col lg:flex-row gap-4 mb-4">
                    <div className="relative flex-grow">
                        <input
                            type="text"
                            placeholder={t('searchBooks')}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full p-3 ps-10 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                        />
                        <i className="fas fa-search absolute start-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <select
                        value={filterBy}
                        onChange={(e) => setFilterBy(e.target.value as 'all' | 'published' | 'draft')}
                        className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                    >
                        <option value="all">{t('allStatuses')}</option>
                        <option value="published">{t('publishedStatus')}</option>
                        <option value="draft">{t('draft')}</option>
                    </select>
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('sortBy')}:</label>
                            <select
                                value={sortBy}
                                onChange={(e) => setSortBy(e.target.value as 'title' | 'author' | 'date' | 'wordCount')}
                                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="date">{t('date')}</option>
                                <option value="title">{t('title')}</option>
                                <option value="author">{t('author')}</option>
                                <option value="wordCount">{t('wordCount')}</option>
                            </select>
                        </div>
                        <button
                            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                            className="p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                            title={sortOrder === 'asc' ? t('ascending') : t('descending')}
                        >
                            <i className={`fas fa-sort-${sortOrder === 'asc' ? 'up' : 'down'}`}></i>
                        </button>
                    </div>

                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                            {filteredAndSortedBooks.length} {t('books')}
                        </span>
                        <div className="flex border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
                            <button
                                onClick={() => setViewMode('grid')}
                                className={`p-2 transition-colors ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700'}`}
                                title={t('gridView')}
                            >
                                <i className="fas fa-th"></i>
                            </button>
                            <button
                                onClick={() => setViewMode('list')}
                                className={`p-2 transition-colors ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700'}`}
                                title={t('listView')}
                            >
                                <i className="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {filteredAndSortedBooks.length > 0 ? (
                <div className={viewMode === 'grid'
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                    : "space-y-4"
                }>
                    {filteredAndSortedBooks.map(book => (
                        <BookCard
                            key={book.id}
                            book={book}
                            onEdit={(id) => navigate(`/editor/${id}`)}
                            onDelete={handleDeleteBook}
                            onRead={(id) => navigate(`/read/${id}`)}
                            onExportPDF={handleExportToPDF}
                            viewMode={viewMode}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-16 bg-white dark:bg-gray-800 rounded-lg shadow-md">
                    <i className="fas fa-box-open text-6xl text-gray-400 dark:text-gray-500"></i>
                    <h3 className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">{t('emptyLibraryTitle')}</h3>
                    <p className="mt-2 text-gray-500 dark:text-gray-400">{t('emptyLibrarySubtitle')}</p>
                </div>
            )}

            <Modal isOpen={isCreateModalOpen} onClose={() => setCreateModalOpen(false)} title={t('createBookTitle')}>
                <div className="space-y-4">
                    <input type="text" placeholder={t('bookTitleLabel')} value={newBookData.title} onChange={(e) => setNewBookData({...newBookData, title: e.target.value})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600" />
                    <input type="text" placeholder={t('authorNameLabel')} value={newBookData.author} onChange={(e) => setNewBookData({...newBookData, author: e.target.value})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600" />
                    <textarea placeholder={t('descriptionLabel')} value={newBookData.description} onChange={(e) => setNewBookData({...newBookData, description: e.target.value})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600" rows={3}></textarea>
                    <select value={newBookData.category} onChange={(e) => setNewBookData({...newBookData, category: e.target.value as BookCategory})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                        {Object.values(BookCategory).map(cat => <option key={cat} value={cat}>{t(cat)}</option>)}
                    </select>
                    <input type="text" placeholder={t('keywordsLabel')} value={newBookData.keywords} onChange={(e) => setNewBookData({...newBookData, keywords: e.target.value})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600" />
                    <div className="flex justify-end space-x-2">
                        <button onClick={() => setCreateModalOpen(false)} className="px-4 py-2 rounded text-gray-600 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500">{t('cancel')}</button>
                        <button onClick={handleCreateBook} className="px-4 py-2 rounded text-white bg-blue-500 hover:bg-blue-600">{t('create')}</button>
                    </div>
                </div>
            </Modal>

            {showFileUpload && (
                <FileUpload
                    onFileImport={handleFileImport}
                    onClose={() => setShowFileUpload(false)}
                />
            )}

            {showPDFExport && selectedBookForPDF && (
                <PDFExport
                    book={selectedBookForPDF}
                    onClose={() => {
                        setShowPDFExport(false);
                        setSelectedBookForPDF(null);
                    }}
                />
            )}
        </div>
    );
};

export default Library;