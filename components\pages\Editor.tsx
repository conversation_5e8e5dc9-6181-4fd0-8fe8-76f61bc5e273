
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useBooks } from '../../hooks/useBooks';
import { Book } from '../../types';
import Modal from '../Modal';
import { useLanguage } from '../../i18n';

const countWords = (htmlString: string) => {
    const text = htmlString.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    if (text === '') return 0;
    return text.split(' ').length;
};

const Editor = () => {
    const { bookId } = useParams<{ bookId: string }>();
    const navigate = useNavigate();
    const { getBookById, updateBook } = useBooks();
    const { t, dir, language } = useLanguage();
    const [book, setBook] = useState<Book | null>(null);
    const [content, setContent] = useState('');
    const [wordCount, setWordCount] = useState(0);
    const [isSaving, setIsSaving] = useState(false);
    const [isPublishModalOpen, setPublishModalOpen] = useState(false);
    const editorRef = useRef<HTMLDivElement>(null);
    
    useEffect(() => {
        if (bookId) {
            const foundBook = getBookById(bookId);
            if (foundBook) {
                setBook(foundBook);
                setContent(foundBook.content);
                setWordCount(foundBook.wordCount);
            } else {
                navigate('/library');
            }
        }
    }, [bookId, getBookById, navigate]);
    
    const saveContent = useCallback(() => {
        if (book && bookId) {
            setIsSaving(true);
            const currentContent = editorRef.current?.innerHTML || '';
            const currentWordCount = countWords(currentContent);
            updateBook(bookId, { content: currentContent, wordCount: currentWordCount });
            setWordCount(currentWordCount);
            setTimeout(() => setIsSaving(false), 1000);
        }
    }, [book, bookId, updateBook]);

    // Auto-save
    useEffect(() => {
        const timer = setTimeout(() => {
            if (book && content !== book.content) {
                saveContent();
            }
        }, 5000); // Auto-save every 5 seconds
        return () => clearTimeout(timer);
    }, [content, book, saveContent]);

    // Keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.ctrlKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        saveContent();
                        break;
                    case 'b':
                        e.preventDefault();
                        document.execCommand('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        document.execCommand('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        document.execCommand('underline');
                        break;
                }
            }
        };
        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [saveContent]);
    
    const handleContentChange = (e: React.FormEvent<HTMLDivElement>) => {
        const newContent = e.currentTarget.innerHTML;
        setContent(newContent);
        setWordCount(countWords(newContent));
    };

    const handleFormat = (command: string) => {
        document.execCommand(command);
        editorRef.current?.focus();
    };

    const handlePublish = () => {
        if (book && bookId) {
            saveContent();
            updateBook(bookId, {
                isPublished: book.isPublished,
                price: book.price,
                keywords: book.keywords,
            });
            setPublishModalOpen(false);
        }
    };

    if (!book) {
        return <div className="text-center p-10">{t('editorLoading')}</div>;
    }
    
    return (
        <div className="max-w-4xl mx-auto">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-3xl font-bold text-gray-800 dark:text-white truncate">{book.title}</h2>
                <div className="flex items-center space-x-4">
                    <button onClick={saveContent} className="px-4 py-2 rounded text-white bg-blue-500 hover:bg-blue-600 flex items-center">
                        <i className={`fas fa-save ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i> {t('save')}
                    </button>
                    <button onClick={() => setPublishModalOpen(true)} className="px-4 py-2 rounded text-white bg-green-500 hover:bg-green-600 flex items-center">
                       <i className={`fas fa-upload ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i> {t('publish')}
                    </button>
                </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
                <div className="flex items-center p-2 border-b border-gray-200 dark:border-gray-700 space-x-1">
                    <button onClick={() => handleFormat('bold')} className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"><i className="fas fa-bold"></i></button>
                    <button onClick={() => handleFormat('italic')} className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"><i className="fas fa-italic"></i></button>
                    <button onClick={() => handleFormat('underline')} className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"><i className="fas fa-underline"></i></button>
                    <button onClick={() => handleFormat('insertUnorderedList')} className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"><i className="fas fa-list-ul"></i></button>
                    <button onClick={() => handleFormat('insertOrderedList')} className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"><i className="fas fa-list-ol"></i></button>
                    <button onClick={() => handleFormat('justifyLeft')} className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"><i className="fas fa-align-left"></i></button>
                    <button onClick={() => handleFormat('justifyCenter')} className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"><i className="fas fa-align-center"></i></button>
                    <button onClick={() => handleFormat('justifyRight')} className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"><i className="fas fa-align-right"></i></button>
                </div>
                <div
                    ref={editorRef}
                    contentEditable
                    onInput={handleContentChange}
                    className="p-6 h-[60vh] overflow-y-auto focus:outline-none dark:text-gray-200"
                    dangerouslySetInnerHTML={{ __html: content }}
                    dir={dir}
                />
                <div className="flex justify-between items-center p-2 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400">
                    <span>{t('words')}: {wordCount}</span>
                    <span className={`transition-opacity ${isSaving ? 'opacity-100' : 'opacity-0'}`}>
                        <i className="fas fa-check-circle text-green-500 me-1"></i>{t('saved')}
                    </span>
                </div>
            </div>
             <Modal isOpen={isPublishModalOpen} onClose={() => setPublishModalOpen(false)} title={t('publishSettingsTitle')}>
                <div className="space-y-4">
                    <div>
                        <label className="block mb-1 font-semibold">{t('statusLabel')}</label>
                        <select
                            value={book.isPublished ? 'published' : 'private'}
                            onChange={(e) => setBook({ ...book, isPublished: e.target.value === 'published' })}
                            className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                        >
                            <option value="private">{t('private')}</option>
                            <option value="published">{t('public')}</option>
                        </select>
                    </div>
                     <div>
                        <label className="block mb-1 font-semibold">{t('pricingLabel')}</label>
                         <div className="flex items-center space-x-2">
                            <select
                                value={book.price === 'free' ? 'free' : 'paid'}
                                onChange={(e) => setBook({ ...book, price: e.target.value === 'free' ? 'free' : (typeof book.price === 'number' ? book.price : 0) })}
                                className="w-1/3 p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="free">{t('free')}</option>
                                <option value="paid">{t('paid')}</option>
                            </select>
                            {book.price !== 'free' && (
                                <input
                                    type="number"
                                    min="0"
                                    value={book.price}
                                    onChange={(e) => setBook({ ...book, price: parseFloat(e.target.value) || 0 })}
                                    className="w-2/3 p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                                />
                            )}
                        </div>
                    </div>
                     <div>
                        <label className="block mb-1 font-semibold">{t('keywordsLabel')}</label>
                        <input
                            type="text"
                            value={book.keywords.join(language === 'ar' ? '، ' : ', ')}
                            onChange={(e) => setBook({ ...book, keywords: e.target.value.split(language === 'ar' ? '،' : ',').map(k => k.trim()) })}
                            className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                        />
                    </div>
                    <div className="flex justify-end space-x-2">
                        <button onClick={() => setPublishModalOpen(false)} className="px-4 py-2 rounded text-gray-600 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500">{t('cancel')}</button>
                        <button onClick={handlePublish} className="px-4 py-2 rounded text-white bg-green-500 hover:bg-green-600">{t('saveSettings')}</button>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default Editor;