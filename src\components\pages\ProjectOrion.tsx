import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../i18n';

const ProjectOrion: React.FC = () => {
    const { t, language, dir } = useLanguage();
    const [activeSection, setActiveSection] = useState<string>('vision');

    // Helper function to safely get translation
    const safeT = (key: string, fallback?: string) => {
        try {
            const translation = t(key);
            return translation === key && fallback ? fallback : translation;
        } catch (error) {
            return fallback || key;
        }
    };

    const sections = [
        {
            id: 'vision',
            icon: 'fas fa-eye',
            title: language === 'ar' ? 'الرؤية والهدف' : 'Vision & Goal'
        },
        {
            id: 'audience',
            icon: 'fas fa-users',
            title: language === 'ar' ? 'الجمهور المستهدف' : 'Target Audience'
        },
        {
            id: 'features',
            icon: 'fas fa-cogs',
            title: language === 'ar' ? 'الميزات الأساسية' : 'Core Features'
        },
        {
            id: 'ux',
            icon: 'fas fa-paint-brush',
            title: language === 'ar' ? 'تجربة المستخدم' : 'User Experience'
        },
        {
            id: 'technical',
            icon: 'fas fa-server',
            title: language === 'ar' ? 'الاعتبارات التقنية' : 'Technical Considerations'
        },
        {
            id: 'business',
            icon: 'fas fa-chart-line',
            title: language === 'ar' ? 'نموذج الأعمال' : 'Business Model'
        }
    ];

    const userPersonas = [
        {
            id: 'academic',
            icon: 'fas fa-graduation-cap',
            title: language === 'ar' ? 'الأكاديمي والباحث (الشخصية أ)' : 'Academic & Researcher (Persona A)',
            examples: language === 'ar'
                ? 'مؤلفو "أساسيات الفيزياء الطبية النووية" و "آفاق بحثية في التصوير الجزيئي"'
                : 'Authors of "Fundamentals of Nuclear Medical Physics" and "Research Horizons in Molecular Imaging"',
            needs: language === 'ar'
                ? 'يحتاج إلى أدوات قوية لإدارة المراجع والاقتباسات، وكتابة المعادلات الرياضية والرموز العلمية، وتنظيم هيكل الكتاب بشكل منهجي، بالإضافة إلى أدوات للتعاون مع مراجعين أو مؤلفين مشاركين.'
                : 'Needs powerful tools for reference and citation management, mathematical equation writing, systematic book structure organization, and collaboration tools with reviewers or co-authors.'
        },
        {
            id: 'professional',
            icon: 'fas fa-user-md',
            title: language === 'ar' ? 'المهني والممارس الصحي (الشخصية ب)' : 'Health Professional & Practitioner (Persona B)',
            examples: language === 'ar'
                ? 'مؤلفو "الدليل العملي لضمان ومراقبة الجودة" و "فيزياء العلاج الإشعاعي الحديث"'
                : 'Authors of "Practical Guide to Quality Assurance and Control" and "Modern Radiation Therapy Physics"',
            needs: language === 'ar'
                ? 'يركز على المحتوى العملي والإجرائي. يحتاج إلى قوالب جاهزة للبروتوكولات، وقوائم المراجعة، وسهولة في إدراج جداول ورسوم بيانية توضيحية عالية الدقة، مع إمكانية تتبع الإصدارات المختلفة.'
                : 'Focuses on practical and procedural content. Needs ready-made templates for protocols, checklists, easy insertion of high-precision tables and diagrams, with version tracking capabilities.'
        },
        {
            id: 'developer',
            icon: 'fas fa-flask',
            title: language === 'ar' ? 'الباحث المطور (الشخصية ج)' : 'Research Developer (Persona C)',
            examples: language === 'ar'
                ? 'مؤلفو "المستحضرات الصيدلانية الإشعاعية" و "التصوير الهجين"'
                : 'Authors of "Radiopharmaceuticals" and "Hybrid Imaging"',
            needs: language === 'ar'
                ? 'يحتاج إلى ميزات متقدمة للتعاون البحثي، وربط مباشر مع قواعد البيانات العلمية، وأدوات لتضمين تصورات البيانات المعقدة، والقدرة على ربط المحتوى بمواد تكميلية خارجية.'
                : 'Needs advanced features for research collaboration, direct linking with scientific databases, tools for complex data visualizations, and ability to link content with external supplementary materials.'
        },
        {
            id: 'popularizer',
            icon: 'fas fa-book-open',
            title: language === 'ar' ? 'المؤلف لغير المتخصصين (الشخصية د)' : 'Science Popularizer (Persona D)',
            examples: language === 'ar'
                ? 'مؤلفو "الطب النووي: كيف تُنقذ الذرة حياة الإنسان" و "رحلة داخل جسم الإنسان"'
                : 'Authors of "Nuclear Medicine: How Atoms Save Human Lives" and "Journey Inside the Human Body"',
            needs: language === 'ar'
                ? 'يحتاج إلى واجهة كتابة بسيطة وخالية من المشتتات، وأدوات لتحليل قابلية القراءة، وسهولة في دمج الصور والرسوم التوضيحية الجذابة، وقوالب مرنة تناسب السرد القصصي.'
                : 'Needs a simple, distraction-free writing interface, readability analysis tools, easy integration of attractive images and illustrations, flexible templates for narrative storytelling.'
        }
    ];

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-800 text-white">
                <div className="container mx-auto px-6 py-8">
                    <div className="flex items-center justify-between mb-6">
                        <Link 
                            to="/" 
                            className="flex items-center text-white hover:text-blue-200 transition-colors"
                        >
                            <i className={`fas fa-arrow-${dir === 'rtl' ? 'right' : 'left'} ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`}></i>
                            {t('backToHome')}
                        </Link>
                        <div className="text-right">
                            <div className="text-sm opacity-90">{t('authorInfo')}</div>
                            <div className="text-xs opacity-75">{t('authorAffiliation')}</div>
                            <div className="text-xs opacity-75">{t('copyright')}</div>
                        </div>
                    </div>
                    
                    <div className="text-center">
                        <h1 className="text-4xl md:text-5xl font-bold mb-4">
                            {language === 'ar'
                                ? 'مشروع أوريون - تطبيق ويب متكامل لتأليف ونشر الكتب'
                                : 'Project Orion - Integrated Book Authoring & Publishing Web Application'
                            }
                        </h1>
                        <p className="text-xl md:text-2xl text-blue-100 mb-6">
                            {language === 'ar'
                                ? 'منصة شاملة مصممة خصيصاً لإنشاء محتوى الهندسة الطبية الحيوية'
                                : 'A comprehensive platform designed specifically for biomedical engineering content creation'
                            }
                        </p>
                        <div className="flex flex-wrap justify-center gap-2 text-sm">
                            <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                <i className="fas fa-envelope mr-1"></i>
                                {t('contactEmail')}
                            </span>
                            <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                <i className="fas fa-phone mr-1"></i>
                                {t('phoneNumbers')}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Navigation */}
            <div className="bg-white dark:bg-gray-800 shadow-md sticky top-0 z-40">
                <div className="container mx-auto px-6">
                    <div className="flex overflow-x-auto py-4 space-x-6">
                        {sections.map(section => (
                            <button
                                key={section.id}
                                onClick={() => setActiveSection(section.id)}
                                className={`flex items-center whitespace-nowrap px-4 py-2 rounded-lg transition-colors ${
                                    activeSection === section.id
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                                }`}
                            >
                                <i className={`${section.icon} ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`}></i>
                                {section.title}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="container mx-auto px-6 py-8">
                {/* Vision and Goal Section */}
                {activeSection === 'vision' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-eye text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {language === 'ar' ? 'الرؤية والهدف من المشروع' : 'Vision & Goal of the Project'}
                                </h2>
                            </div>
                            <div className="prose prose-lg max-w-none dark:prose-invert">
                                <p className="text-gray-600 dark:text-gray-300 leading-relaxed text-lg">
                                    {language === 'ar'
                                        ? 'يهدف المشروع إلى تطوير تطبيق ويب شامل ومتكامل يُعنى بتأليف الكتب، مصمم خصيصًا لتمكين فئات متنوعة من المؤلفين من إنشاء محتوى عالي الجودة بكفاءة وسهولة. المنصة ستكون مرنة بما يكفي لدعم متطلبات الكتابة الأكاديمية المتخصصة، والأدلة المهنية العملية، والأبحاث العلمية المتقدمة، وكتب التوعية العامة، كل ذلك ضمن بيئة عمل واحدة وموحدة.'
                                        : 'The project aims to develop a comprehensive and integrated web application for book authoring, specifically designed to enable diverse categories of authors to create high-quality content efficiently and easily. The platform will be flexible enough to support specialized academic writing requirements, practical professional guides, advanced scientific research, and general awareness books, all within a unified work environment.'
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Target Audience Section */}
                {activeSection === 'audience' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-users text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {language === 'ar' ? 'الجمهور المستهدف (شخصيات المستخدمين)' : 'Target Audience (User Personas)'}
                                </h2>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {userPersonas.map(persona => (
                                    <div key={persona.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                        <div className="flex items-center mb-4">
                                            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                <i className={`${persona.icon} text-white`}></i>
                                            </div>
                                            <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                                                {persona.title}
                                            </h3>
                                        </div>
                                        <div className="space-y-3">
                                            <div>
                                                <h4 className="font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                                    {language === 'ar' ? 'أمثلة:' : 'Examples:'}
                                                </h4>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {persona.examples}
                                                </p>
                                            </div>
                                            <div>
                                                <h4 className="font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                                    {language === 'ar' ? 'الاحتياجات:' : 'Needs:'}
                                                </h4>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {persona.needs}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                {/* Core Features Section */}
                {activeSection === 'features' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-cogs text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {language === 'ar' ? 'الميزات والوظائف الأساسية' : 'Core Features & Functions'}
                                </h2>
                            </div>

                            <div className="space-y-8">
                                {/* General Features */}
                                <div>
                                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                                        {language === 'ar' ? 'الميزات العامة (متوفرة لجميع المستخدمين)' : 'General Features (Available to All Users)'}
                                    </h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                            <div className="flex items-center mb-3">
                                                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                    <i className="fas fa-edit text-white text-sm"></i>
                                                </div>
                                                <h4 className="font-semibold text-gray-800 dark:text-white">
                                                    {language === 'ar' ? 'محرر نصوص ذكي' : 'Smart Text Editor'}
                                                </h4>
                                            </div>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {language === 'ar'
                                                    ? 'محرر بسيط وقوي يدعم الكتابة من اليمين إلى اليسار مع خيارات تنسيق متقدمة'
                                                    : 'Simple yet powerful editor with full RTL support and advanced formatting options'
                                                }
                                            </p>
                                        </div>

                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                            <div className="flex items-center mb-3">
                                                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                    <i className="fas fa-cloud text-white text-sm"></i>
                                                </div>
                                                <h4 className="font-semibold text-gray-800 dark:text-white">
                                                    {language === 'ar' ? 'الحفظ السحابي التلقائي' : 'Automatic Cloud Save'}
                                                </h4>
                                            </div>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {language === 'ar'
                                                    ? 'يتم حفظ كل تغيير بشكل فوري على السحابة لضمان عدم فقدان أي عمل'
                                                    : 'Every change is saved instantly to the cloud to ensure no work is lost'
                                                }
                                            </p>
                                        </div>

                                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                            <div className="flex items-center mb-3">
                                                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                    <i className="fas fa-users-cog text-white text-sm"></i>
                                                </div>
                                                <h4 className="font-semibold text-gray-800 dark:text-white">
                                                    {language === 'ar' ? 'التعاون المباشر' : 'Real-time Collaboration'}
                                                </h4>
                                            </div>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {language === 'ar'
                                                    ? 'دعوة محررين أو مؤلفين مشاركين للعمل على نفس المستند في الوقت الفعلي'
                                                    : 'Invite editors or co-authors to work on the same document in real-time'
                                                }
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* User Experience Section */}
                {activeSection === 'ux' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-pink-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-paint-brush text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {language === 'ar' ? 'تجربة المستخدم وواجهة التصميم' : 'User Experience & Design Interface'}
                                </h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <div className="flex items-center">
                                        <i className="fas fa-eye text-blue-600 mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {language === 'ar' ? 'البساطة والتركيز' : 'Simplicity & Focus'}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {language === 'ar'
                                            ? 'واجهة نظيفة وحديثة مع وضع الكتابة الخالية من المشتتات الذي يخفي جميع القوائم غير الضرورية'
                                            : 'Clean and modern interface with distraction-free writing mode that hides all unnecessary menus'
                                        }
                                    </p>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-center">
                                        <i className="fas fa-language text-blue-600 mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {language === 'ar' ? 'دعم كامل للغة العربية' : 'Full Arabic Support'}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {language === 'ar'
                                            ? 'الواجهة والميزات مصممة بشكل أساسي لدعم اتجاه النص من اليمين إلى اليسار دون أي مشاكل'
                                            : 'Interface and features designed primarily to support right-to-left (RTL) text direction without any issues'
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Technical Considerations Section */}
                {activeSection === 'technical' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-server text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {language === 'ar' ? 'الاعتبارات التقنية' : 'Technical Considerations'}
                                </h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <div className="flex items-center mb-4">
                                        <i className="fas fa-cloud text-blue-600 text-2xl mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {language === 'ar' ? 'بنية سحابية' : 'Cloud-Native Architecture'}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {language === 'ar'
                                            ? 'بناء التطبيق على بنية تحتية سحابية لضمان التوافر العالي وقابلية التوسع'
                                            : 'Application built on cloud infrastructure to ensure high availability and scalability'
                                        }
                                    </p>
                                </div>

                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <div className="flex items-center mb-4">
                                        <i className="fas fa-shield-alt text-green-600 text-2xl mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {language === 'ar' ? 'الأمان والخصوصية' : 'Security & Privacy'}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {language === 'ar'
                                            ? 'ضمان حماية حقوق الملكية الفكرية للمؤلفين من خلال التشفير والنسخ الاحتياطي الآمن'
                                            : 'Ensure protection of authors\' intellectual property through encryption and secure backup'
                                        }
                                    </p>
                                </div>

                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <div className="flex items-center mb-4">
                                        <i className="fas fa-plug text-purple-600 text-2xl mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {language === 'ar' ? 'التكامل' : 'Integrations'}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {language === 'ar'
                                            ? 'إمكانية التكامل مع خدمات طرف ثالث مثل Google Drive وDropbox للتخزين وGrammarly للتدقيق اللغوي'
                                            : 'Ability to integrate with third-party services like Google Drive, Dropbox for storage, and Grammarly for proofreading'
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Business Model Section */}
                {activeSection === 'business' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-chart-line text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {language === 'ar' ? 'نموذج الربحية المقترح' : 'Proposed Business Model'}
                                </h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {/* Free Plan */}
                                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-lg p-6 border-2 border-transparent hover:border-blue-500 transition-all">
                                    <div className="text-center mb-6">
                                        <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i className="fas fa-gift text-white text-2xl"></i>
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                                            {language === 'ar' ? 'الخطة المجانية' : 'Free Plan'}
                                        </h3>
                                        <div className="text-3xl font-bold text-blue-600 mb-4">
                                            {language === 'ar' ? 'مجاني' : 'Free'}
                                        </div>
                                    </div>
                                    <div className="space-y-3">
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'الوصول إلى الميزات الأساسية' : 'Access to basic features'}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'مشروع كتاب واحد' : 'One book project'}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'خيارات تصدير محدودة' : 'Limited export options'}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Writer Plan */}
                                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 rounded-lg p-6 border-2 border-blue-500 transition-all">
                                    <div className="text-center mb-6">
                                        <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i className="fas fa-pen text-white text-2xl"></i>
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                                            {language === 'ar' ? 'خطة الكاتب' : 'Writer Plan'}
                                        </h3>
                                        <div className="text-3xl font-bold text-blue-600 mb-4">
                                            {language === 'ar' ? '9.99$ شهرياً' : '$9.99/month'}
                                        </div>
                                    </div>
                                    <div className="space-y-3">
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'مشاريع غير محدودة' : 'Unlimited projects'}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'جميع الميزات العامة' : 'All general features'}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'وحدة متخصصة واحدة' : 'One specialized module'}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Professional Plan */}
                                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 rounded-lg p-6 border-2 border-transparent hover:border-purple-500 transition-all">
                                    <div className="text-center mb-6">
                                        <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i className="fas fa-crown text-white text-2xl"></i>
                                        </div>
                                        <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                                            {language === 'ar' ? 'خطة المحترفين' : 'Professional Plan'}
                                        </h3>
                                        <div className="text-3xl font-bold text-purple-600 mb-4">
                                            {language === 'ar' ? '19.99$ شهرياً' : '$19.99/month'}
                                        </div>
                                    </div>
                                    <div className="space-y-3">
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'جميع الوحدات المتخصصة' : 'All specialized modules'}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'ميزات تعاون متقدمة' : 'Advanced collaboration'}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <i className="fas fa-check text-green-600 mr-2"></i>
                                            <span className="text-gray-600 dark:text-gray-300">
                                                {language === 'ar' ? 'دعم فني مخصص' : 'Dedicated support'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Footer */}
            <footer className="bg-gray-800 text-white py-8 mt-16">
                <div className="container mx-auto px-6 text-center">
                    <div className="mb-4">
                        <h3 className="text-xl font-bold mb-2">
                            {language === 'ar'
                                ? 'مشروع أوريون - تطبيق ويب متكامل لتأليف ونشر الكتب'
                                : 'Project Orion - Integrated Book Authoring & Publishing Web Application'
                            }
                        </h3>
                        <p className="text-gray-300">
                            {language === 'ar'
                                ? 'منصة شاملة مصممة خصيصاً لإنشاء محتوى الهندسة الطبية الحيوية'
                                : 'A comprehensive platform designed specifically for biomedical engineering content creation'
                            }
                        </p>
                        <div className="mt-4">
                            <Link
                                to="/"
                                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors inline-flex items-center"
                            >
                                <i className={`fas fa-home ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`}></i>
                                {language === 'ar' ? 'العودة للرئيسية' : 'Back to Home'}
                            </Link>
                        </div>
                    </div>
                    <div className="border-t border-gray-700 pt-4">
                        <p className="text-sm text-gray-400">
                            {language === 'ar'
                                ? 'د. محمد يعقوب إسماعيل - جامعة السودان للعلوم والتكنولوجيا - الهندسة الطبية الحيوية - © 2025 جميع الحقوق محفوظة'
                                : 'Dr. Mohammed Yagoub Esmail - SUST - BME - © 2025 All Rights Reserved'
                            }
                        </p>
                        <div className="flex justify-center space-x-4 mt-2">
                            <span className="text-sm text-gray-400">
                                <i className="fas fa-envelope mr-1"></i>
                                <EMAIL>
                            </span>
                            <span className="text-sm text-gray-400">
                                <i className="fas fa-phone mr-1"></i>
                                +249912867327, +966538076790
                            </span>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default ProjectOrion;
