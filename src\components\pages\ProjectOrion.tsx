import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../i18n';

const ProjectOrion: React.FC = () => {
    const { t, language, dir } = useLanguage();
    const [activeSection, setActiveSection] = useState<string>('vision');

    const sections = [
        { id: 'vision', icon: 'fas fa-eye', titleKey: 'visionAndGoal' },
        { id: 'audience', icon: 'fas fa-users', titleKey: 'targetAudience' },
        { id: 'features', icon: 'fas fa-cogs', titleKey: 'coreFeatures' },
        { id: 'ux', icon: 'fas fa-paint-brush', titleKey: 'userExperience' },
        { id: 'technical', icon: 'fas fa-server', titleKey: 'technicalConsiderations' },
        { id: 'business', icon: 'fas fa-chart-line', titleKey: 'businessModel' }
    ];

    const userPersonas = [
        {
            id: 'academic',
            icon: 'fas fa-graduation-cap',
            titleKey: 'academicResearcher',
            examplesKey: 'academicExamples',
            needsKey: 'academicNeeds'
        },
        {
            id: 'professional',
            icon: 'fas fa-user-md',
            titleKey: 'healthProfessional',
            examplesKey: 'professionalExamples',
            needsKey: 'professionalNeeds'
        },
        {
            id: 'developer',
            icon: 'fas fa-flask',
            titleKey: 'researchDeveloper',
            examplesKey: 'developerExamples',
            needsKey: 'developerNeeds'
        },
        {
            id: 'popularizer',
            icon: 'fas fa-book-open',
            titleKey: 'sciencePopularizer',
            examplesKey: 'popularizerExamples',
            needsKey: 'popularizerNeeds'
        }
    ];

    const coreFeatures = [
        {
            category: 'general',
            titleKey: 'generalFeatures',
            features: [
                { icon: 'fas fa-edit', titleKey: 'smartTextEditor', descKey: 'smartTextEditorDesc' },
                { icon: 'fas fa-sitemap', titleKey: 'structuralOrganization', descKey: 'structuralOrganizationDesc' },
                { icon: 'fas fa-cloud', titleKey: 'autoCloudSave', descKey: 'autoCloudSaveDesc' },
                { icon: 'fas fa-target', titleKey: 'goalTracking', descKey: 'goalTrackingDesc' },
                { icon: 'fas fa-file-export', titleKey: 'multipleExports', descKey: 'multipleExportsDesc' },
                { icon: 'fas fa-users-cog', titleKey: 'realTimeCollaboration', descKey: 'realTimeCollaborationDesc' }
            ]
        },
        {
            category: 'academic',
            titleKey: 'academicWritingModule',
            features: [
                { icon: 'fas fa-bookmark', titleKey: 'referenceManager', descKey: 'referenceManagerDesc' },
                { icon: 'fas fa-calculator', titleKey: 'equationEditor', descKey: 'equationEditorDesc' },
                { icon: 'fas fa-search', titleKey: 'reviewTools', descKey: 'reviewToolsDesc' },
                { icon: 'fas fa-shield-alt', titleKey: 'plagiarismChecker', descKey: 'plagiarismCheckerDesc' }
            ]
        },
        {
            category: 'professional',
            titleKey: 'professionalGuidesModule',
            features: [
                { icon: 'fas fa-layer-group', titleKey: 'templateLibrary', descKey: 'templateLibraryDesc' },
                { icon: 'fas fa-chart-bar', titleKey: 'diagramTools', descKey: 'diagramToolsDesc' },
                { icon: 'fas fa-code-branch', titleKey: 'versionControl', descKey: 'versionControlDesc' }
            ]
        },
        {
            category: 'public',
            titleKey: 'publicBooksModule',
            features: [
                { icon: 'fas fa-glasses', titleKey: 'readabilityAnalyzer', descKey: 'readabilityAnalyzerDesc' },
                { icon: 'fas fa-photo-video', titleKey: 'multimediaIntegration', descKey: 'multimediaIntegrationDesc' },
                { icon: 'fas fa-palette', titleKey: 'attractiveTemplates', descKey: 'attractiveTemplatesDesc' }
            ]
        }
    ];

    const pricingPlans = [
        {
            id: 'free',
            titleKey: 'freePlan',
            priceKey: 'freePrice',
            featuresKey: 'freePlanFeatures',
            icon: 'fas fa-gift'
        },
        {
            id: 'writer',
            titleKey: 'writerPlan',
            priceKey: 'writerPrice',
            featuresKey: 'writerPlanFeatures',
            icon: 'fas fa-pen'
        },
        {
            id: 'professional',
            titleKey: 'professionalPlan',
            priceKey: 'professionalPrice',
            featuresKey: 'professionalPlanFeatures',
            icon: 'fas fa-crown'
        }
    ];

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-800 text-white">
                <div className="container mx-auto px-6 py-8">
                    <div className="flex items-center justify-between mb-6">
                        <Link 
                            to="/" 
                            className="flex items-center text-white hover:text-blue-200 transition-colors"
                        >
                            <i className={`fas fa-arrow-${dir === 'rtl' ? 'right' : 'left'} ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`}></i>
                            {t('backToHome')}
                        </Link>
                        <div className="text-right">
                            <div className="text-sm opacity-90">{t('authorInfo')}</div>
                            <div className="text-xs opacity-75">{t('authorAffiliation')}</div>
                            <div className="text-xs opacity-75">{t('copyright')}</div>
                        </div>
                    </div>
                    
                    <div className="text-center">
                        <h1 className="text-4xl md:text-5xl font-bold mb-4">
                            {t('projectOrionTitle')}
                        </h1>
                        <p className="text-xl md:text-2xl text-blue-100 mb-6">
                            {t('projectOrionSubtitle')}
                        </p>
                        <div className="flex flex-wrap justify-center gap-2 text-sm">
                            <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                <i className="fas fa-envelope mr-1"></i>
                                {t('contactEmail')}
                            </span>
                            <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                <i className="fas fa-phone mr-1"></i>
                                {t('phoneNumbers')}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Navigation */}
            <div className="bg-white dark:bg-gray-800 shadow-md sticky top-0 z-40">
                <div className="container mx-auto px-6">
                    <div className="flex overflow-x-auto py-4 space-x-6">
                        {sections.map(section => (
                            <button
                                key={section.id}
                                onClick={() => setActiveSection(section.id)}
                                className={`flex items-center whitespace-nowrap px-4 py-2 rounded-lg transition-colors ${
                                    activeSection === section.id
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                                }`}
                            >
                                <i className={`${section.icon} ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`}></i>
                                {t(section.titleKey)}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="container mx-auto px-6 py-8">
                {/* Vision and Goal Section */}
                {activeSection === 'vision' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-eye text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {t('visionAndGoal')}
                                </h2>
                            </div>
                            <div className="prose prose-lg max-w-none dark:prose-invert">
                                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                                    {t('visionDescription')}
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Target Audience Section */}
                {activeSection === 'audience' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-users text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {t('targetAudience')}
                                </h2>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {userPersonas.map(persona => (
                                    <div key={persona.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                        <div className="flex items-center mb-4">
                                            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                <i className={`${persona.icon} text-white`}></i>
                                            </div>
                                            <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                                                {t(persona.titleKey)}
                                            </h3>
                                        </div>
                                        <div className="space-y-3">
                                            <div>
                                                <h4 className="font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                                    {t('examples')}:
                                                </h4>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {t(persona.examplesKey)}
                                                </p>
                                            </div>
                                            <div>
                                                <h4 className="font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                                    {t('needs')}:
                                                </h4>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {t(persona.needsKey)}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                {/* Core Features Section */}
                {activeSection === 'features' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-cogs text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {t('coreFeatures')}
                                </h2>
                            </div>

                            {coreFeatures.map(category => (
                                <div key={category.category} className="mb-8">
                                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                                        {t(category.titleKey)}
                                    </h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {category.features.map((feature, index) => (
                                            <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                                <div className="flex items-center mb-3">
                                                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                                        <i className={`${feature.icon} text-white text-sm`}></i>
                                                    </div>
                                                    <h4 className="font-semibold text-gray-800 dark:text-white">
                                                        {t(feature.titleKey)}
                                                    </h4>
                                                </div>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {t(feature.descKey)}
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* User Experience Section */}
                {activeSection === 'ux' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-pink-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-paint-brush text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {t('userExperience')}
                                </h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <div className="flex items-center">
                                        <i className="fas fa-eye text-blue-600 mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {t('simplicityAndFocus')}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {t('simplicityAndFocusDesc')}
                                    </p>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-center">
                                        <i className="fas fa-cog text-blue-600 mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {t('customization')}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {t('customizationDesc')}
                                    </p>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-center">
                                        <i className="fas fa-mobile-alt text-blue-600 mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {t('responsiveDesign')}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {t('responsiveDesignDesc')}
                                    </p>
                                </div>

                                <div className="space-y-4">
                                    <div className="flex items-center">
                                        <i className="fas fa-language text-blue-600 mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {t('fullArabicSupport')}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {t('fullArabicSupportDesc')}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Technical Considerations Section */}
                {activeSection === 'technical' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-server text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {t('technicalConsiderations')}
                                </h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <div className="flex items-center mb-4">
                                        <i className="fas fa-cloud text-blue-600 text-2xl mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {t('cloudNativeArchitecture')}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {t('cloudNativeArchitectureDesc')}
                                    </p>
                                </div>

                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <div className="flex items-center mb-4">
                                        <i className="fas fa-shield-alt text-green-600 text-2xl mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {t('securityAndPrivacy')}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {t('securityAndPrivacyDesc')}
                                    </p>
                                </div>

                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <div className="flex items-center mb-4">
                                        <i className="fas fa-plug text-purple-600 text-2xl mr-3"></i>
                                        <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                                            {t('integrations')}
                                        </h3>
                                    </div>
                                    <p className="text-gray-600 dark:text-gray-300">
                                        {t('integrationsDesc')}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Business Model Section */}
                {activeSection === 'business' && (
                    <div className="space-y-8">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                            <div className="flex items-center mb-6">
                                <div className="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center mr-4">
                                    <i className="fas fa-chart-line text-white text-xl"></i>
                                </div>
                                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">
                                    {t('businessModel')}
                                </h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {pricingPlans.map(plan => (
                                    <div key={plan.id} className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-lg p-6 border-2 border-transparent hover:border-blue-500 transition-all">
                                        <div className="text-center mb-6">
                                            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                                <i className={`${plan.icon} text-white text-2xl`}></i>
                                            </div>
                                            <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                                                {t(plan.titleKey)}
                                            </h3>
                                            <div className="text-3xl font-bold text-blue-600 mb-4">
                                                {t(plan.priceKey)}
                                            </div>
                                        </div>
                                        <div className="space-y-3">
                                            {t(plan.featuresKey).split('\n').map((feature: string, index: number) => (
                                                <div key={index} className="flex items-center">
                                                    <i className="fas fa-check text-green-600 mr-2"></i>
                                                    <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Footer */}
            <footer className="bg-gray-800 text-white py-8 mt-16">
                <div className="container mx-auto px-6 text-center">
                    <div className="mb-4">
                        <h3 className="text-xl font-bold mb-2">{t('projectOrionTitle')}</h3>
                        <p className="text-gray-300">{t('projectOrionSubtitle')}</p>
                        <div className="mt-4">
                            <Link
                                to="/"
                                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors inline-flex items-center"
                            >
                                <i className={`fas fa-home ${dir === 'rtl' ? 'ml-2' : 'mr-2'}`}></i>
                                {t('backToHome')}
                            </Link>
                        </div>
                    </div>
                    <div className="border-t border-gray-700 pt-4">
                        <p className="text-sm text-gray-400">
                            {t('authorInfo')} - {t('authorAffiliation')} - {t('copyright')}
                        </p>
                        <div className="flex justify-center space-x-4 mt-2">
                            <span className="text-sm text-gray-400">
                                <i className="fas fa-envelope mr-1"></i>
                                {t('contactEmail')}
                            </span>
                            <span className="text-sm text-gray-400">
                                <i className="fas fa-phone mr-1"></i>
                                {t('phoneNumbers')}
                            </span>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default ProjectOrion;
