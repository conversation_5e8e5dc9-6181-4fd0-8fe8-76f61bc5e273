import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../i18n';
import { useBooks } from '../../hooks/useBooks';

const Home = () => {
    const { t, dir } = useLanguage();
    const { books } = useBooks();
    
    const publishedBooks = books.filter(book => book.isPublished);
    const totalWords = books.reduce((sum, book) => sum + book.wordCount, 0);
    
    const features = [
        {
            icon: 'fas fa-edit',
            title: t('richTextEditor'),
            description: t('richTextEditorDesc')
        },
        {
            icon: 'fas fa-language',
            title: t('bilingualSupport'),
            description: t('bilingualSupportDesc')
        },
        {
            icon: 'fas fa-books',
            title: t('bookManagement'),
            description: t('bookManagementDesc')
        },
        {
            icon: 'fas fa-cloud-upload-alt',
            title: t('publishingSystem'),
            description: t('publishingSystemDesc')
        },
        {
            icon: 'fas fa-mobile-alt',
            title: t('responsiveDesign'),
            description: t('responsiveDesignDesc')
        },
        {
            icon: 'fas fa-save',
            title: t('localStorage'),
            description: t('localStorageDesc')
        }
    ];

    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <section className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 text-white py-20">
                <div className="container mx-auto px-6 text-center">
                    <div className="max-w-4xl mx-auto">
                        <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                            {t('heroTitle')}
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">
                            {t('heroSubtitle')}
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                            <Link 
                                to="/library" 
                                className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold py-4 px-8 rounded-lg text-lg transition-all transform hover:scale-105 shadow-lg"
                            >
                                <i className="fas fa-rocket me-2"></i>
                                {t('getStarted')}
                            </Link>
                            <Link 
                                to="/discovery" 
                                className="border-2 border-white hover:bg-white hover:text-blue-600 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all"
                            >
                                <i className="fas fa-compass me-2"></i>
                                {t('exploreBooks')}
                            </Link>
                        </div>
                    </div>
                </div>
            </section>

            {/* Stats Section */}
            <section className="py-16 bg-gray-50 dark:bg-gray-800">
                <div className="container mx-auto px-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                        <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-8 rounded-lg shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i className="fas fa-book text-white text-2xl"></i>
                            </div>
                            <div className="text-4xl font-bold mb-2">{books.length}</div>
                            <div className="text-blue-100">{t('totalBooks')}</div>
                            <div className="mt-4 text-sm text-blue-100">
                                <i className="fas fa-chart-line mr-2"></i>
                                {t('personalLibrary')}
                            </div>
                        </div>
                        <div className="bg-gradient-to-br from-green-500 to-green-600 p-8 rounded-lg shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i className="fas fa-check-circle text-white text-2xl"></i>
                            </div>
                            <div className="text-4xl font-bold mb-2">{publishedBooks.length}</div>
                            <div className="text-green-100">{t('published')}</div>
                            <div className="mt-4 text-sm text-green-100">
                                <i className="fas fa-globe mr-2"></i>
                                {t('availableForReading')}
                            </div>
                        </div>
                        <div className="bg-gradient-to-br from-purple-500 to-purple-600 p-8 rounded-lg shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i className="fas fa-file-word text-white text-2xl"></i>
                            </div>
                            <div className="text-4xl font-bold mb-2">{totalWords.toLocaleString()}</div>
                            <div className="text-purple-100">{t('totalWords')}</div>
                            <div className="mt-4 text-sm text-purple-100">
                                <i className="fas fa-pen-fancy mr-2"></i>
                                {t('richContent')}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20 bg-white dark:bg-gray-900">
                <div className="container mx-auto px-6">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl font-bold text-gray-800 dark:text-white mb-4">
                            {t('advancedFeaturesTitle')}
                        </h2>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            {t('advancedFeaturesSubtitle')}
                        </p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {features.map((feature, index) => (
                            <div key={index} className="bg-gray-50 dark:bg-gray-800 p-8 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                                <div className="text-4xl text-blue-600 mb-4">
                                    <i className={feature.icon}></i>
                                </div>
                                <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-3">
                                    {feature.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                                    {feature.description}
                                </p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <div className="container mx-auto px-6 text-center">
                    <h2 className="text-4xl font-bold mb-6">
                        هل أنت مستعد لبدء رحلة التأليف؟
                    </h2>
                    <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
                        انضم إلى مجتمع المؤلفين والباحثين في مجال الهندسة الطبية الحيوية وابدأ في إنشاء محتوى علمي متميز
                    </p>
                    <Link 
                        to="/library" 
                        className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold py-4 px-8 rounded-lg text-lg transition-all transform hover:scale-105 shadow-lg inline-flex items-center"
                    >
                        <i className="fas fa-pen-fancy me-2"></i>
                        ابدأ الكتابة الآن
                    </Link>
                </div>
            </section>

            {/* Recent Books Section */}
            {publishedBooks.length > 0 && (
                <section className="py-20 bg-gray-50 dark:bg-gray-800">
                    <div className="container mx-auto px-6">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
                                {t('recentBooksTitle')}
                            </h2>
                            <p className="text-gray-600 dark:text-gray-300">
                                {t('recentBooksSubtitle')}
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {publishedBooks.slice(0, 3).map(book => (
                                <div key={book.id} className="bg-white dark:bg-gray-700 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                                    <div className="h-2 bg-gradient-to-r from-blue-500 to-purple-600"></div>
                                    <div className="p-6">
                                        <div className="flex items-center gap-3 mb-4">
                                            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                                <i className="fas fa-book text-white"></i>
                                            </div>
                                            <div className="flex-grow">
                                                <h3 className="text-xl font-bold text-gray-800 dark:text-white line-clamp-2">
                                                    {book.title}
                                                </h3>
                                                <p className="text-gray-600 dark:text-gray-300 text-sm">
                                                    {t('by')} {book.author}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="mb-4">
                                            <span className="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full mb-2">
                                                {book.category}
                                            </span>
                                        </div>
                                        <p className="text-gray-500 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                                            {book.description}
                                        </p>
                                        <div className="flex justify-between items-center">
                                            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                                                <span className="flex items-center gap-1">
                                                    <i className="fas fa-file-word"></i>
                                                    {book.wordCount.toLocaleString()} {t('words')}
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <i className="fas fa-calendar"></i>
                                                    {new Date(book.updatedAt).toLocaleDateString('ar-SA')}
                                                </span>
                                            </div>
                                            <Link
                                                to={`/read/${book.id}`}
                                                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
                                            >
                                                <i className="fas fa-book-open"></i>
                                                {t('readNow')}
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                        
                        <div className="text-center mt-8">
                            <Link 
                                to="/discovery"
                                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors"
                            >
                                {t('exploreBooks')}
                            </Link>
                        </div>
                    </div>
                </section>
            )}
        </div>
    );
};

export default Home;
