/* General Styles */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --white-color: #fff;
    --font-family: 'Cairo', sans-serif; /* A nice Arabic font */
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white-color);
    margin: 0;
    padding: 0;
    direction: rtl;
}

.container {
    width: 90%;
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.main-header {
    background-color: var(--white-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-links {
    list-style: none;
    display: flex;
    gap: 2rem;
    margin: 0;
}

.nav-links a {
    text-decoration: none;
    color: var(--dark-color);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white-color);
    border: 1px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
}

.btn-lg {
    font-size: 1.1rem;
    padding: 1rem 2rem;
}

/* Hero Section */
.hero-section {
    text-align: center;
    padding: 6rem 0;
    background-color: var(--light-color);
}

.hero-section h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.hero-section .subtitle {
    font-size: 1.3rem;
    color: var(--secondary-color);
    max-width: 600px;
    margin: 0 auto 2rem;
}

.hero-image {
    margin-top: 3rem;
}

.hero-image img {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

/* How it Works Section */
.how-it-works-section {
    padding: 5rem 0;
    text-align: center;
}

.how-it-works-section h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.steps-container {
    display: flex;
    justify-content: space-around;
    gap: 2rem;
}

.step {
    max-width: 300px;
}

.step-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.step h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Features Section */
.features-section {
    padding: 5rem 0;
    background-color: var(--light-color);
}

.features-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-item {
    background-color: var(--white-color);
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.feature-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-item h3 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
}

/* CTA Section */
.cta-section {
    background-color: var(--primary-color);
    color: var(--white-color);
    text-align: center;
    padding: 5rem 0;
}

.cta-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.cta-section .btn-primary {
    background-color: var(--white-color);
    color: var(--primary-color);
    border-color: var(--white-color);
}

.cta-section .btn-primary:hover {
    background-color: #f0f0f0;
}

/* Footer */
.main-footer-landing {
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-about, .footer-links, .footer-social {
    flex: 1;
    min-width: 250px;
}

.footer-about h3 {
    color: var(--primary-color);
}

.footer-links h4, .footer-social h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links a {
    color: var(--light-color);
    text-decoration: none;
    line-height: 2;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-social a {
    color: var(--light-color);
    font-size: 1.5rem;
    margin-left: 1rem;
    transition: color 0.3s ease;
}

.footer-social a:hover {
    color: var(--primary-color);
}

.footer-author {
    margin-top: 1.5rem;
    font-size: 0.9rem;
}

.footer-author p {
    margin: 0.3rem 0;
}

.footer-contact p {
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
}

.footer-contact i {
    margin-left: 10px;
    color: var(--primary-color);
}

.footer-contact a {
    color: var(--light-color);
    text-decoration: none;
}

.footer-contact a:hover {
    text-decoration: underline;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--secondary-color);
}

/* Responsive */
@media (max-width: 768px) {
    .nav-links {
        display: none; /* Simple responsive solution */
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .steps-container {
        flex-direction: column;
        align-items: center;
    }
}
