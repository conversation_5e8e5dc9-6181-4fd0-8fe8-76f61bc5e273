import React, { useState } from 'react';
import { useLanguage } from '../i18n';

interface AdvancedTableEditorProps {
    onInsertTable: (tableHTML: string) => void;
    onClose: () => void;
}

const AdvancedTableEditor: React.FC<AdvancedTableEditorProps> = ({ onInsertTable, onClose }) => {
    const { t, dir } = useLanguage();
    const [rows, setRows] = useState(3);
    const [cols, setCols] = useState(3);
    const [hasHeader, setHasHeader] = useState(true);
    const [borderStyle, setBorderStyle] = useState('1px solid #d1d5db');
    const [cellPadding, setCellPadding] = useState('8px');
    const [tableWidth, setTableWidth] = useState('100%');
    const [alignment, setAlignment] = useState<'left' | 'center' | 'right'>('left');

    const generateTable = () => {
        let tableHTML = `<table style="width: ${tableWidth}; border-collapse: collapse; margin: 1rem 0; text-align: ${alignment};">`;
        
        // Generate header row if enabled
        if (hasHeader) {
            tableHTML += '<thead><tr>';
            for (let j = 0; j < cols; j++) {
                tableHTML += `<th style="border: ${borderStyle}; padding: ${cellPadding}; background-color: #f3f4f6; font-weight: 600;">${t('header')} ${j + 1}</th>`;
            }
            tableHTML += '</tr></thead>';
        }
        
        // Generate body rows
        tableHTML += '<tbody>';
        const startRow = hasHeader ? 1 : 0;
        for (let i = startRow; i < rows + startRow; i++) {
            tableHTML += '<tr>';
            for (let j = 0; j < cols; j++) {
                tableHTML += `<td style="border: ${borderStyle}; padding: ${cellPadding};">${t('cell')} ${i + 1}-${j + 1}</td>`;
            }
            tableHTML += '</tr>';
        }
        tableHTML += '</tbody></table>';
        
        return tableHTML;
    };

    const handleInsert = () => {
        const tableHTML = generateTable();
        onInsertTable(tableHTML);
        onClose();
    };

    const previewTable = generateTable();

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('advancedTable')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Settings Panel */}
                    <div className="space-y-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">{t('tableSettings')}</h4>
                        
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium mb-2">{t('rows')}</label>
                                <input
                                    type="number"
                                    min="1"
                                    max="20"
                                    value={rows}
                                    onChange={(e) => setRows(parseInt(e.target.value) || 1)}
                                    className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium mb-2">{t('columns')}</label>
                                <input
                                    type="number"
                                    min="1"
                                    max="10"
                                    value={cols}
                                    onChange={(e) => setCols(parseInt(e.target.value) || 1)}
                                    className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                                />
                            </div>
                        </div>

                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="hasHeader"
                                checked={hasHeader}
                                onChange={(e) => setHasHeader(e.target.checked)}
                                className="mr-2"
                            />
                            <label htmlFor="hasHeader" className="text-sm font-medium">
                                {t('includeHeader')}
                            </label>
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-2">{t('tableWidth')}</label>
                            <select
                                value={tableWidth}
                                onChange={(e) => setTableWidth(e.target.value)}
                                className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="100%">100%</option>
                                <option value="75%">75%</option>
                                <option value="50%">50%</option>
                                <option value="auto">Auto</option>
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-2">{t('alignment')}</label>
                            <select
                                value={alignment}
                                onChange={(e) => setAlignment(e.target.value as 'left' | 'center' | 'right')}
                                className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="left">{t('alignLeft')}</option>
                                <option value="center">{t('alignCenter')}</option>
                                <option value="right">{t('alignRight')}</option>
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-2">{t('cellPadding')}</label>
                            <select
                                value={cellPadding}
                                onChange={(e) => setCellPadding(e.target.value)}
                                className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="4px">Small (4px)</option>
                                <option value="8px">Medium (8px)</option>
                                <option value="12px">Large (12px)</option>
                                <option value="16px">Extra Large (16px)</option>
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium mb-2">{t('borderStyle')}</label>
                            <select
                                value={borderStyle}
                                onChange={(e) => setBorderStyle(e.target.value)}
                                className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="1px solid #d1d5db">Solid Gray</option>
                                <option value="1px solid #000000">Solid Black</option>
                                <option value="2px solid #3b82f6">Thick Blue</option>
                                <option value="1px dashed #6b7280">Dashed</option>
                                <option value="none">No Border</option>
                            </select>
                        </div>
                    </div>

                    {/* Preview Panel */}
                    <div className="space-y-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">{t('preview')}</h4>
                        <div 
                            className="border rounded p-4 bg-gray-50 dark:bg-gray-700 overflow-auto max-h-96"
                            dir={dir}
                        >
                            <div dangerouslySetInnerHTML={{ __html: previewTable }} />
                        </div>
                    </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 rounded text-gray-600 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500"
                    >
                        {t('cancel')}
                    </button>
                    <button
                        onClick={handleInsert}
                        className="px-4 py-2 rounded text-white bg-blue-500 hover:bg-blue-600"
                    >
                        {t('insertTable')}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AdvancedTableEditor;
