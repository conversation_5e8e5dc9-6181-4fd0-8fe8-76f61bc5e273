import React, { useState } from 'react';
import { useLanguage } from '../i18n';

interface AdvancedPDFExportProps {
    content: string;
    title: string;
    onClose: () => void;
}

const AdvancedPDFExport: React.FC<AdvancedPDFExportProps> = ({ content, title, onClose }) => {
    const { t, language } = useLanguage();
    const [exportSettings, setExportSettings] = useState({
        paperSize: 'A4',
        orientation: 'portrait',
        margins: 'normal',
        fontSize: '12',
        fontFamily: 'Times New Roman',
        includeHeader: true,
        includeFooter: true,
        includePageNumbers: true,
        includeTableOfContents: false,
        includeCoverPage: true,
        watermark: '',
        backgroundColor: '#ffffff',
        textColor: '#000000'
    });

    const [authorInfo] = useState({
        name: 'Dr. <PERSON>go<PERSON> Esmail',
        affiliation: 'SUST-BME',
        email: '<EMAIL>',
        phone: '+249912867327, +966538076790',
        copyright: '@2025'
    });

    const paperSizes = [
        { value: 'A4', label: 'A4 (210 × 297 mm)', labelAr: 'A4 (210 × 297 مم)' },
        { value: 'A3', label: 'A3 (297 × 420 mm)', labelAr: 'A3 (297 × 420 مم)' },
        { value: 'Letter', label: 'Letter (8.5 × 11 in)', labelAr: 'Letter (8.5 × 11 بوصة)' },
        { value: 'Legal', label: 'Legal (8.5 × 14 in)', labelAr: 'Legal (8.5 × 14 بوصة)' }
    ];

    const orientations = [
        { value: 'portrait', label: 'Portrait', labelAr: 'عمودي' },
        { value: 'landscape', label: 'Landscape', labelAr: 'أفقي' }
    ];

    const marginOptions = [
        { value: 'narrow', label: 'Narrow (0.5 in)', labelAr: 'ضيق (0.5 بوصة)' },
        { value: 'normal', label: 'Normal (1 in)', labelAr: 'عادي (1 بوصة)' },
        { value: 'wide', label: 'Wide (1.5 in)', labelAr: 'واسع (1.5 بوصة)' }
    ];

    const fontFamilies = [
        'Times New Roman',
        'Arial',
        'Calibri',
        'Georgia',
        'Verdana',
        'Helvetica'
    ];

    const generatePDFStyles = () => {
        const marginMap = {
            narrow: '0.5in',
            normal: '1in',
            wide: '1.5in'
        };

        return `
            @page {
                size: ${exportSettings.paperSize} ${exportSettings.orientation};
                margin: ${marginMap[exportSettings.margins as keyof typeof marginMap]};
            }
            
            body {
                font-family: ${exportSettings.fontFamily};
                font-size: ${exportSettings.fontSize}pt;
                line-height: 1.6;
                color: ${exportSettings.textColor};
                background-color: ${exportSettings.backgroundColor};
                margin: 0;
                padding: 0;
            }
            
            .pdf-header {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                height: 50px;
                background: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                padding: 10px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 10pt;
            }
            
            .pdf-footer {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                height: 40px;
                background: #f8f9fa;
                border-top: 1px solid #dee2e6;
                padding: 10px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 10pt;
            }
            
            .pdf-content {
                margin-top: ${exportSettings.includeHeader ? '70px' : '0'};
                margin-bottom: ${exportSettings.includeFooter ? '60px' : '0'};
                padding: 20px;
            }
            
            .cover-page {
                page-break-after: always;
                text-align: center;
                padding-top: 200px;
            }
            
            .cover-title {
                font-size: 24pt;
                font-weight: bold;
                margin-bottom: 30px;
                color: #2c3e50;
            }
            
            .cover-author {
                font-size: 16pt;
                margin-bottom: 20px;
                color: #34495e;
            }
            
            .cover-affiliation {
                font-size: 14pt;
                margin-bottom: 10px;
                color: #7f8c8d;
            }
            
            .cover-contact {
                font-size: 12pt;
                color: #95a5a6;
                margin-top: 50px;
            }
            
            .watermark {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 72pt;
                color: rgba(0, 0, 0, 0.1);
                z-index: -1;
                pointer-events: none;
            }
            
            h1, h2, h3, h4, h5, h6 {
                color: #2c3e50;
                page-break-after: avoid;
            }
            
            table {
                page-break-inside: avoid;
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
            }
            
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            
            .math-equation {
                text-align: center;
                margin: 1em 0;
                padding: 0.5em;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
            }
            
            @media print {
                .no-print {
                    display: none !important;
                }
            }
        `;
    };

    const generatePDFContent = () => {
        const currentDate = new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US');
        
        let htmlContent = `
            <!DOCTYPE html>
            <html lang="${language}" dir="${language === 'ar' ? 'rtl' : 'ltr'}">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title}</title>
                <style>${generatePDFStyles()}</style>
            </head>
            <body>
        `;

        // Add watermark if specified
        if (exportSettings.watermark) {
            htmlContent += `<div class="watermark">${exportSettings.watermark}</div>`;
        }

        // Add header
        if (exportSettings.includeHeader) {
            htmlContent += `
                <div class="pdf-header">
                    <div>${title}</div>
                    <div>${currentDate}</div>
                </div>
            `;
        }

        // Add footer
        if (exportSettings.includeFooter) {
            htmlContent += `
                <div class="pdf-footer">
                    <div>${authorInfo.name} - ${authorInfo.affiliation}</div>
                    ${exportSettings.includePageNumbers ? '<div class="page-number"></div>' : ''}
                </div>
            `;
        }

        htmlContent += '<div class="pdf-content">';

        // Add cover page
        if (exportSettings.includeCoverPage) {
            htmlContent += `
                <div class="cover-page">
                    <div class="cover-title">${title}</div>
                    <div class="cover-author">${t('author')}: ${authorInfo.name}</div>
                    <div class="cover-affiliation">${authorInfo.affiliation}</div>
                    <div class="cover-contact">
                        ${t('email')}: ${authorInfo.email}<br>
                        ${t('phone')}: ${authorInfo.phone}<br>
                        ${t('copyright')} ${authorInfo.copyright}
                    </div>
                </div>
            `;
        }

        // Add table of contents if specified
        if (exportSettings.includeTableOfContents) {
            const headings = content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi) || [];
            if (headings.length > 0) {
                htmlContent += `
                    <div class="table-of-contents" style="page-break-after: always;">
                        <h2>${t('tableOfContents')}</h2>
                        <ul>
                `;
                headings.forEach((heading, index) => {
                    const text = heading.replace(/<[^>]*>/g, '');
                    htmlContent += `<li><a href="#heading-${index}">${text}</a></li>`;
                });
                htmlContent += '</ul></div>';
            }
        }

        // Add main content with heading anchors for TOC
        let processedContent = content;
        if (exportSettings.includeTableOfContents) {
            processedContent = content.replace(/<h([1-6])([^>]*)>(.*?)<\/h[1-6]>/gi, (match, level, attrs, text, index) => {
                return `<h${level}${attrs} id="heading-${index}">${text}</h${level}>`;
            });
        }

        htmlContent += processedContent;
        htmlContent += '</div></body></html>';

        return htmlContent;
    };

    const handleExportToPDF = () => {
        const pdfContent = generatePDFContent();
        const printWindow = window.open('', '_blank');
        
        if (printWindow) {
            printWindow.document.write(pdfContent);
            printWindow.document.close();
            
            // Wait for content to load then trigger print
            printWindow.onload = () => {
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            };
        }
    };

    const handlePreview = () => {
        const pdfContent = generatePDFContent();
        const previewWindow = window.open('', '_blank');
        
        if (previewWindow) {
            previewWindow.document.write(pdfContent);
            previewWindow.document.close();
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('advancedPDFExport')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Settings Panel */}
                    <div className="space-y-4">
                        <h4 className="font-medium text-gray-900 dark:text-white">{t('exportSettings')}</h4>
                        
                        {/* Paper Size */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {t('paperSize')}
                            </label>
                            <select
                                value={exportSettings.paperSize}
                                onChange={(e) => setExportSettings(prev => ({ ...prev, paperSize: e.target.value }))}
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                            >
                                {paperSizes.map(size => (
                                    <option key={size.value} value={size.value}>
                                        {language === 'ar' ? size.labelAr : size.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Orientation */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {t('orientation')}
                            </label>
                            <select
                                value={exportSettings.orientation}
                                onChange={(e) => setExportSettings(prev => ({ ...prev, orientation: e.target.value }))}
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                            >
                                {orientations.map(orientation => (
                                    <option key={orientation.value} value={orientation.value}>
                                        {language === 'ar' ? orientation.labelAr : orientation.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Margins */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {t('margins')}
                            </label>
                            <select
                                value={exportSettings.margins}
                                onChange={(e) => setExportSettings(prev => ({ ...prev, margins: e.target.value }))}
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                            >
                                {marginOptions.map(margin => (
                                    <option key={margin.value} value={margin.value}>
                                        {language === 'ar' ? margin.labelAr : margin.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Font Settings */}
                        <div className="grid grid-cols-2 gap-2">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('fontSize')}
                                </label>
                                <select
                                    value={exportSettings.fontSize}
                                    onChange={(e) => setExportSettings(prev => ({ ...prev, fontSize: e.target.value }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    {[10, 11, 12, 14, 16, 18, 20].map(size => (
                                        <option key={size} value={size}>{size}pt</option>
                                    ))}
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('fontFamily')}
                                </label>
                                <select
                                    value={exportSettings.fontFamily}
                                    onChange={(e) => setExportSettings(prev => ({ ...prev, fontFamily: e.target.value }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    {fontFamilies.map(font => (
                                        <option key={font} value={font}>{font}</option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* Checkboxes */}
                        <div className="space-y-2">
                            {[
                                { key: 'includeHeader', label: t('includeHeader') },
                                { key: 'includeFooter', label: t('includeFooter') },
                                { key: 'includePageNumbers', label: t('includePageNumbers') },
                                { key: 'includeTableOfContents', label: t('includeTableOfContents') },
                                { key: 'includeCoverPage', label: t('includeCoverPage') }
                            ].map(option => (
                                <label key={option.key} className="flex items-center">
                                    <input
                                        type="checkbox"
                                        checked={exportSettings[option.key as keyof typeof exportSettings] as boolean}
                                        onChange={(e) => setExportSettings(prev => ({ 
                                            ...prev, 
                                            [option.key]: e.target.checked 
                                        }))}
                                        className="mr-2"
                                    />
                                    <span className="text-sm text-gray-700 dark:text-gray-300">
                                        {option.label}
                                    </span>
                                </label>
                            ))}
                        </div>

                        {/* Watermark */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {t('watermark')} ({t('optional')})
                            </label>
                            <input
                                type="text"
                                value={exportSettings.watermark}
                                onChange={(e) => setExportSettings(prev => ({ ...prev, watermark: e.target.value }))}
                                placeholder={t('enterWatermarkText')}
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                            />
                        </div>
                    </div>

                    {/* Preview and Actions */}
                    <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-4">{t('preview')}</h4>
                        
                        <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-700 mb-4 h-64 overflow-y-auto">
                            <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                                {t('documentPreview')}
                            </div>
                            <div 
                                className="prose dark:prose-invert max-w-none text-xs"
                                dangerouslySetInnerHTML={{ __html: content.substring(0, 500) + '...' }}
                            />
                        </div>

                        <div className="space-y-3">
                            <button
                                onClick={handlePreview}
                                className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors flex items-center justify-center"
                            >
                                <i className="fas fa-eye mr-2"></i>
                                {t('previewPDF')}
                            </button>
                            
                            <button
                                onClick={handleExportToPDF}
                                className="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors flex items-center justify-center"
                            >
                                <i className="fas fa-download mr-2"></i>
                                {t('exportToPDF')}
                            </button>
                        </div>

                        {/* Author Information Display */}
                        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                            <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                                {t('authorInformation')}
                            </h5>
                            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                <div><strong>{t('author')}:</strong> {authorInfo.name}</div>
                                <div><strong>{t('affiliation')}:</strong> {authorInfo.affiliation}</div>
                                <div><strong>{t('email')}:</strong> {authorInfo.email}</div>
                                <div><strong>{t('phone')}:</strong> {authorInfo.phone}</div>
                                <div><strong>{t('copyright')}:</strong> {authorInfo.copyright}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdvancedPDFExport;
