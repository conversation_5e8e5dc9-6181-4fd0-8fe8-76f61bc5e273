import React, { useState } from 'react';
import { useLanguage } from '../../i18n';
import AdvancedTextEditor from '../AdvancedTextEditor';
import PhysicsTemplatesLibrary from '../PhysicsTemplatesLibrary';
import EquationEditor from '../EquationEditor';
import Modal from '../Modal';

const AdvancedEditorDemo: React.FC = () => {
    const { language, dir } = useLanguage();
    const [content, setContent] = useState<string>('');
    const [showTemplates, setShowTemplates] = useState(false);
    const [showEquations, setShowEquations] = useState(false);
    const [wordCount, setWordCount] = useState(0);

    const countWords = (htmlString: string) => {
        const text = htmlString.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
        if (text === '') return 0;
        return text.split(' ').length;
    };

    const handleContentChange = (newContent: string) => {
        setContent(newContent);
        setWordCount(countWords(newContent));
    };

    const handleTemplateSelect = (templateContent: string) => {
        setContent(content + templateContent);
        setWordCount(countWords(content + templateContent));
        setShowTemplates(false);
    };

    const handleEquationInsert = (equation: any) => {
        setContent(content + equation.html);
        setWordCount(countWords(content + equation.html));
        setShowEquations(false);
    };

    const clearContent = () => {
        setContent('');
        setWordCount(0);
    };

    const sampleContent = language === 'ar' 
        ? `<h1>مرحباً بك في المحرر المتقدم</h1>
           <p>هذا المحرر مصمم خصيصاً للكتابة في مجال الفيزياء والهندسة الطبية الحيوية.</p>
           <h2>الميزات المتاحة:</h2>
           <ul>
               <li>قوالب متخصصة للفيزياء الطبية</li>
               <li>معادلات رياضية جاهزة</li>
               <li>أدوات تنسيق متقدمة</li>
               <li>دعم كامل للغة العربية</li>
           </ul>
           <p>ابدأ بالكتابة أو استخدم القوالب والمعادلات المتاحة!</p>`
        : `<h1>Welcome to the Advanced Editor</h1>
           <p>This editor is specifically designed for writing in physics and biomedical engineering.</p>
           <h2>Available Features:</h2>
           <ul>
               <li>Specialized medical physics templates</li>
               <li>Ready-made mathematical equations</li>
               <li>Advanced formatting tools</li>
               <li>Full Arabic language support</li>
           </ul>
           <p>Start writing or use the available templates and equations!</p>`;

    const loadSampleContent = () => {
        setContent(sampleContent);
        setWordCount(countWords(sampleContent));
    };

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
            <div className="container mx-auto px-4">
                {/* Header */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
                                {language === 'ar' 
                                    ? 'المحرر المتقدم للفيزياء والهندسة الطبية الحيوية'
                                    : 'Advanced Physics & Biomedical Engineering Editor'
                                }
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400">
                                {language === 'ar'
                                    ? 'محرر نصوص متقدم مع قوالب متخصصة ومعادلات رياضية'
                                    : 'Advanced text editor with specialized templates and mathematical equations'
                                }
                            </p>
                        </div>
                        <div className="flex gap-2 mt-4 md:mt-0">
                            <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">
                                {language === 'ar' ? 'الكلمات:' : 'Words:'} {wordCount}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 mb-6">
                    <div className="flex flex-wrap gap-3">
                        <button
                            onClick={() => setShowTemplates(true)}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i className="fas fa-file-medical mr-2"></i>
                            {language === 'ar' ? 'قوالب الفيزياء والهندسة' : 'Physics & Engineering Templates'}
                        </button>
                        
                        <button
                            onClick={() => setShowEquations(true)}
                            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i className="fas fa-square-root-alt mr-2"></i>
                            {language === 'ar' ? 'المعادلات الرياضية' : 'Mathematical Equations'}
                        </button>
                        
                        <button
                            onClick={loadSampleContent}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i className="fas fa-file-alt mr-2"></i>
                            {language === 'ar' ? 'تحميل محتوى تجريبي' : 'Load Sample Content'}
                        </button>
                        
                        <button
                            onClick={clearContent}
                            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i className="fas fa-trash mr-2"></i>
                            {language === 'ar' ? 'مسح المحتوى' : 'Clear Content'}
                        </button>
                    </div>
                </div>

                {/* Editor */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                    <div className="p-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
                            {language === 'ar' ? 'منطقة الكتابة' : 'Writing Area'}
                        </h2>
                    </div>
                    
                    <div className="p-6">
                        <AdvancedTextEditor
                            content={content}
                            onChange={handleContentChange}
                            placeholder={language === 'ar' 
                                ? 'ابدأ بكتابة محتواك هنا... يمكنك استخدام القوالب والمعادلات المتاحة لتسريع عملية الكتابة.'
                                : 'Start writing your content here... You can use the available templates and equations to speed up your writing process.'
                            }
                        />
                    </div>
                </div>

                {/* Features Overview */}
                <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <div className="flex items-center mb-4">
                            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
                                <i className="fas fa-file-medical text-blue-600 dark:text-blue-400 text-xl"></i>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                                {language === 'ar' ? 'قوالب متخصصة' : 'Specialized Templates'}
                            </h3>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400">
                            {language === 'ar'
                                ? 'مجموعة شاملة من القوالب للفيزياء الطبية والهندسة الطبية الحيوية والتصوير الطبي'
                                : 'Comprehensive collection of templates for medical physics, biomedical engineering, and medical imaging'
                            }
                        </p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <div className="flex items-center mb-4">
                            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-4">
                                <i className="fas fa-square-root-alt text-purple-600 dark:text-purple-400 text-xl"></i>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                                {language === 'ar' ? 'معادلات رياضية' : 'Mathematical Equations'}
                            </h3>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400">
                            {language === 'ar'
                                ? 'مكتبة واسعة من المعادلات الرياضية والفيزيائية مع شرح مفصل لكل معادلة'
                                : 'Extensive library of mathematical and physics equations with detailed explanations'
                            }
                        </p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <div className="flex items-center mb-4">
                            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4">
                                <i className="fas fa-language text-green-600 dark:text-green-400 text-xl"></i>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                                {language === 'ar' ? 'دعم ثنائي اللغة' : 'Bilingual Support'}
                            </h3>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400">
                            {language === 'ar'
                                ? 'دعم كامل للغة العربية والإنجليزية مع اتجاه النص المناسب لكل لغة'
                                : 'Full support for Arabic and English with appropriate text direction for each language'
                            }
                        </p>
                    </div>
                </div>
            </div>

            {/* Templates Modal */}
            <Modal isOpen={showTemplates} onClose={() => setShowTemplates(false)}>
                <PhysicsTemplatesLibrary onSelectTemplate={handleTemplateSelect} />
            </Modal>

            {/* Equations Modal */}
            <Modal isOpen={showEquations} onClose={() => setShowEquations(false)}>
                <EquationEditor onInsertEquation={handleEquationInsert} />
            </Modal>
        </div>
    );
};

export default AdvancedEditorDemo;
