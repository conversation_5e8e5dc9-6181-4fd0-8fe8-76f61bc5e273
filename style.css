/* General Styles */
body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.8;
    color: #333;
    background-color: #f4f7f9;
    margin: 0;
    direction: rtl;
}

.container {
    width: 90%;
    max-width: 960px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    background-color: #2c3e50;
    color: #fff;
    padding: 2rem 0;
    text-align: center;
}

header h1 {
    margin: 0;
    font-size: 2.5rem;
}

header p {
    margin: 0;
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Main Content */
main {
    padding: 2rem 0;
}

section {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    margin-bottom: 2.5rem;
    padding: 2rem;
}

section h2 {
    font-size: 2rem;
    color: #3498db;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 0.5rem;
    margin-top: 0;
}

/* Code Blocks and Textareas */
.code-block {
    background-color: #2d3436;
    color: #dfe6e9;
    padding: 1.5rem;
    border-radius: 5px;
    margin: 1rem 0;
    overflow-x: auto;
}

.code-block pre, .code-block code {
    font-family: 'Courier New', Courier, monospace;
    font-size: 1rem;
}

.file-editor {
    margin-top: 1.5rem;
}

.file-editor h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.file-editor p {
    font-size: 1rem;
    color: #555;
}

textarea {
    width: 100%;
    min-height: 250px;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.95rem;
    line-height: 1.5;
    resize: vertical;
    box-sizing: border-box; /* Important for padding */
}

textarea[readonly] {
    background-color: #ecf0f1;
    cursor: not-allowed;
}

/* Buttons */
.btn {
    display: inline-block;
    background-color: #3498db;
    color: #fff;
    padding: 12px 25px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #2980b9;
}

/* Lists */
ul, ol {
    line-height: 1.8;
}

/* Footer */
footer {
    text-align: center;
    padding: 2rem 0;
    background-color: #2c3e50;
    color: #fff;
}
