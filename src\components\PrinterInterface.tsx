import React, { useState, useEffect } from 'react';
import { useLanguage } from '../i18n';

interface PrinterInterfaceProps {
    content: string;
    title: string;
    onClose: () => void;
}

interface PrinterInfo {
    id: string;
    name: string;
    status: 'ready' | 'busy' | 'offline' | 'error';
    type: 'laser' | 'inkjet' | 'thermal' | 'dot-matrix';
    location?: string;
    paperSizes: string[];
    colorSupport: boolean;
    duplexSupport: boolean;
}

const PrinterInterface: React.FC<PrinterInterfaceProps> = ({ content, title, onClose }) => {
    const { t, language } = useLanguage();
    const [availablePrinters, setAvailablePrinters] = useState<PrinterInfo[]>([]);
    const [selectedPrinter, setSelectedPrinter] = useState<string>('');
    const [printSettings, setPrintSettings] = useState({
        copies: 1,
        paperSize: 'A4',
        orientation: 'portrait',
        colorMode: 'color',
        quality: 'normal',
        duplex: 'none',
        pageRange: 'all',
        customRange: '',
        collate: true,
        margins: 'normal'
    });
    const [printQueue, setPrintQueue] = useState<any[]>([]);
    const [isScanning, setIsScanning] = useState(false);
    const [printPreview, setPrintPreview] = useState<string>('');

    // Mock printer discovery (in real implementation, this would use system APIs)
    useEffect(() => {
        scanForPrinters();
    }, []);

    const scanForPrinters = async () => {
        setIsScanning(true);
        
        // Simulate printer discovery
        setTimeout(() => {
            const mockPrinters: PrinterInfo[] = [
                {
                    id: 'hp-laserjet-1',
                    name: 'HP LaserJet Pro M404n',
                    status: 'ready',
                    type: 'laser',
                    location: 'Office - Room 101',
                    paperSizes: ['A4', 'A3', 'Letter', 'Legal'],
                    colorSupport: false,
                    duplexSupport: true
                },
                {
                    id: 'canon-pixma-1',
                    name: 'Canon PIXMA TS8320',
                    status: 'ready',
                    type: 'inkjet',
                    location: 'Office - Room 102',
                    paperSizes: ['A4', 'Letter', '4x6', '5x7'],
                    colorSupport: true,
                    duplexSupport: true
                },
                {
                    id: 'epson-workforce-1',
                    name: 'Epson WorkForce Pro WF-4830',
                    status: 'busy',
                    type: 'inkjet',
                    location: 'Office - Room 103',
                    paperSizes: ['A4', 'A3', 'Letter', 'Legal'],
                    colorSupport: true,
                    duplexSupport: true
                },
                {
                    id: 'brother-laser-1',
                    name: 'Brother HL-L2350DW',
                    status: 'offline',
                    type: 'laser',
                    location: 'Office - Room 104',
                    paperSizes: ['A4', 'Letter', 'Legal'],
                    colorSupport: false,
                    duplexSupport: true
                }
            ];
            
            setAvailablePrinters(mockPrinters);
            if (mockPrinters.length > 0) {
                setSelectedPrinter(mockPrinters[0].id);
            }
            setIsScanning(false);
        }, 2000);
    };

    const generatePrintPreview = () => {
        const selectedPrinterInfo = availablePrinters.find(p => p.id === selectedPrinter);
        if (!selectedPrinterInfo) return;

        const previewStyles = `
            <style>
                @page {
                    size: ${printSettings.paperSize} ${printSettings.orientation};
                    margin: ${printSettings.margins === 'narrow' ? '0.5in' : printSettings.margins === 'wide' ? '1.5in' : '1in'};
                }
                body {
                    font-family: Arial, sans-serif;
                    font-size: 12pt;
                    line-height: 1.6;
                    color: ${printSettings.colorMode === 'grayscale' ? '#333' : '#000'};
                }
                .print-header {
                    text-align: center;
                    border-bottom: 1px solid #ccc;
                    padding-bottom: 10px;
                    margin-bottom: 20px;
                }
                .print-footer {
                    text-align: center;
                    border-top: 1px solid #ccc;
                    padding-top: 10px;
                    margin-top: 20px;
                    font-size: 10pt;
                }
                @media print {
                    .no-print { display: none !important; }
                }
            </style>
        `;

        const previewContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                ${previewStyles}
            </head>
            <body>
                <div class="print-header">
                    <h1>${title}</h1>
                    <p>Dr. Mohammed Yagoub Esmail - SUST-BME</p>
                </div>
                <div class="print-content">
                    ${content}
                </div>
                <div class="print-footer">
                    <p>© 2025 - <EMAIL> - +249912867327, +966538076790</p>
                </div>
            </body>
            </html>
        `;

        setPrintPreview(previewContent);
    };

    useEffect(() => {
        if (selectedPrinter && content) {
            generatePrintPreview();
        }
    }, [selectedPrinter, printSettings, content, title]);

    const handlePrint = async () => {
        const selectedPrinterInfo = availablePrinters.find(p => p.id === selectedPrinter);
        if (!selectedPrinterInfo) {
            alert(t('pleaseSelectPrinter'));
            return;
        }

        if (selectedPrinterInfo.status !== 'ready') {
            alert(t('printerNotReady'));
            return;
        }

        // Create print job
        const printJob = {
            id: Date.now().toString(),
            title: title,
            printer: selectedPrinterInfo.name,
            copies: printSettings.copies,
            pages: printSettings.pageRange === 'all' ? 'All' : printSettings.customRange,
            status: 'queued',
            timestamp: new Date().toLocaleString()
        };

        setPrintQueue(prev => [...prev, printJob]);

        // In a real implementation, this would send the job to the actual printer
        // For now, we'll simulate the printing process
        setTimeout(() => {
            setPrintQueue(prev => prev.map(job => 
                job.id === printJob.id ? { ...job, status: 'printing' } : job
            ));
        }, 1000);

        setTimeout(() => {
            setPrintQueue(prev => prev.map(job => 
                job.id === printJob.id ? { ...job, status: 'completed' } : job
            ));
        }, 5000);

        // Open print dialog for browser printing
        if (printPreview) {
            const printWindow = window.open('', '_blank');
            if (printWindow) {
                printWindow.document.write(printPreview);
                printWindow.document.close();
                printWindow.onload = () => {
                    setTimeout(() => {
                        printWindow.print();
                    }, 500);
                };
            }
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'ready': return 'fas fa-check-circle text-green-500';
            case 'busy': return 'fas fa-clock text-yellow-500';
            case 'offline': return 'fas fa-times-circle text-red-500';
            case 'error': return 'fas fa-exclamation-triangle text-red-500';
            default: return 'fas fa-question-circle text-gray-500';
        }
    };

    const getPrinterTypeIcon = (type: string) => {
        switch (type) {
            case 'laser': return 'fas fa-print';
            case 'inkjet': return 'fas fa-print';
            case 'thermal': return 'fas fa-receipt';
            case 'dot-matrix': return 'fas fa-print';
            default: return 'fas fa-print';
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-6xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('printerInterface')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Printer Selection */}
                    <div>
                        <div className="flex justify-between items-center mb-4">
                            <h4 className="font-medium text-gray-900 dark:text-white">{t('availablePrinters')}</h4>
                            <button
                                onClick={scanForPrinters}
                                disabled={isScanning}
                                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm transition-colors"
                            >
                                <i className={`fas fa-search ${isScanning ? 'animate-spin' : ''} mr-1`}></i>
                                {isScanning ? t('scanning') : t('scan')}
                            </button>
                        </div>

                        <div className="space-y-2 mb-6">
                            {availablePrinters.map(printer => (
                                <div
                                    key={printer.id}
                                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                                        selectedPrinter === printer.id
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                                    }`}
                                    onClick={() => setSelectedPrinter(printer.id)}
                                >
                                    <div className="flex items-center justify-between mb-2">
                                        <div className="flex items-center">
                                            <i className={`${getPrinterTypeIcon(printer.type)} mr-2`}></i>
                                            <span className="font-medium">{printer.name}</span>
                                        </div>
                                        <i className={getStatusIcon(printer.status)}></i>
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        <div>{t('status')}: {t(printer.status)}</div>
                                        {printer.location && <div>{t('location')}: {printer.location}</div>}
                                        <div>{t('type')}: {t(printer.type)}</div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Print Queue */}
                        <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('printQueue')}</h4>
                            <div className="space-y-2 max-h-32 overflow-y-auto">
                                {printQueue.length === 0 ? (
                                    <p className="text-gray-500 dark:text-gray-400 text-sm">{t('noPrintJobs')}</p>
                                ) : (
                                    printQueue.map(job => (
                                        <div key={job.id} className="p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm">
                                            <div className="flex justify-between items-center">
                                                <span className="font-medium">{job.title}</span>
                                                <span className={`px-2 py-1 rounded text-xs ${
                                                    job.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                    job.status === 'printing' ? 'bg-blue-100 text-blue-800' :
                                                    'bg-yellow-100 text-yellow-800'
                                                }`}>
                                                    {t(job.status)}
                                                </span>
                                            </div>
                                            <div className="text-gray-600 dark:text-gray-400">
                                                {job.printer} • {job.copies} {t('copies')} • {job.pages}
                                            </div>
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Print Settings */}
                    <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-4">{t('printSettings')}</h4>
                        
                        <div className="space-y-4">
                            {/* Copies */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('copies')}
                                </label>
                                <input
                                    type="number"
                                    min="1"
                                    max="999"
                                    value={printSettings.copies}
                                    onChange={(e) => setPrintSettings(prev => ({ ...prev, copies: parseInt(e.target.value) || 1 }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                />
                            </div>

                            {/* Paper Size */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('paperSize')}
                                </label>
                                <select
                                    value={printSettings.paperSize}
                                    onChange={(e) => setPrintSettings(prev => ({ ...prev, paperSize: e.target.value }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    <option value="A4">A4</option>
                                    <option value="A3">A3</option>
                                    <option value="Letter">Letter</option>
                                    <option value="Legal">Legal</option>
                                </select>
                            </div>

                            {/* Orientation */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('orientation')}
                                </label>
                                <select
                                    value={printSettings.orientation}
                                    onChange={(e) => setPrintSettings(prev => ({ ...prev, orientation: e.target.value }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    <option value="portrait">{t('portrait')}</option>
                                    <option value="landscape">{t('landscape')}</option>
                                </select>
                            </div>

                            {/* Color Mode */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('colorMode')}
                                </label>
                                <select
                                    value={printSettings.colorMode}
                                    onChange={(e) => setPrintSettings(prev => ({ ...prev, colorMode: e.target.value }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    <option value="color">{t('color')}</option>
                                    <option value="grayscale">{t('grayscale')}</option>
                                    <option value="blackwhite">{t('blackAndWhite')}</option>
                                </select>
                            </div>

                            {/* Quality */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('quality')}
                                </label>
                                <select
                                    value={printSettings.quality}
                                    onChange={(e) => setPrintSettings(prev => ({ ...prev, quality: e.target.value }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    <option value="draft">{t('draft')}</option>
                                    <option value="normal">{t('normal')}</option>
                                    <option value="high">{t('high')}</option>
                                </select>
                            </div>

                            {/* Duplex */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('duplex')}
                                </label>
                                <select
                                    value={printSettings.duplex}
                                    onChange={(e) => setPrintSettings(prev => ({ ...prev, duplex: e.target.value }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    <option value="none">{t('singleSided')}</option>
                                    <option value="long">{t('doubleSidedLong')}</option>
                                    <option value="short">{t('doubleSidedShort')}</option>
                                </select>
                            </div>

                            {/* Page Range */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {t('pageRange')}
                                </label>
                                <select
                                    value={printSettings.pageRange}
                                    onChange={(e) => setPrintSettings(prev => ({ ...prev, pageRange: e.target.value }))}
                                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                >
                                    <option value="all">{t('allPages')}</option>
                                    <option value="current">{t('currentPage')}</option>
                                    <option value="custom">{t('customRange')}</option>
                                </select>
                                {printSettings.pageRange === 'custom' && (
                                    <input
                                        type="text"
                                        placeholder="1-5, 8, 11-13"
                                        value={printSettings.customRange}
                                        onChange={(e) => setPrintSettings(prev => ({ ...prev, customRange: e.target.value }))}
                                        className="w-full mt-2 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                                    />
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Preview and Actions */}
                    <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-4">{t('preview')}</h4>
                        
                        <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-700 mb-4 h-64 overflow-y-auto">
                            {printPreview ? (
                                <iframe
                                    srcDoc={printPreview}
                                    className="w-full h-full border-0"
                                    title="Print Preview"
                                />
                            ) : (
                                <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                                    <div className="text-center">
                                        <i className="fas fa-file-alt text-4xl mb-4"></i>
                                        <p>{t('selectPrinterToPreview')}</p>
                                    </div>
                                </div>
                            )}
                        </div>

                        <div className="space-y-3">
                            <button
                                onClick={handlePrint}
                                disabled={!selectedPrinter || availablePrinters.find(p => p.id === selectedPrinter)?.status !== 'ready'}
                                className="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors flex items-center justify-center"
                            >
                                <i className="fas fa-print mr-2"></i>
                                {t('print')}
                            </button>
                            
                            <button
                                onClick={() => {
                                    if (printPreview) {
                                        const previewWindow = window.open('', '_blank');
                                        if (previewWindow) {
                                            previewWindow.document.write(printPreview);
                                            previewWindow.document.close();
                                        }
                                    }
                                }}
                                className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors flex items-center justify-center"
                            >
                                <i className="fas fa-eye mr-2"></i>
                                {t('fullPreview')}
                            </button>
                        </div>

                        {/* Printer Info */}
                        {selectedPrinter && (
                            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                                {(() => {
                                    const printer = availablePrinters.find(p => p.id === selectedPrinter);
                                    return printer ? (
                                        <div>
                                            <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                                                {t('selectedPrinter')}
                                            </h5>
                                            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                                <div><strong>{t('name')}:</strong> {printer.name}</div>
                                                <div><strong>{t('status')}:</strong> {t(printer.status)}</div>
                                                <div><strong>{t('type')}:</strong> {t(printer.type)}</div>
                                                <div><strong>{t('colorSupport')}:</strong> {printer.colorSupport ? t('yes') : t('no')}</div>
                                                <div><strong>{t('duplexSupport')}:</strong> {printer.duplexSupport ? t('yes') : t('no')}</div>
                                            </div>
                                        </div>
                                    ) : null;
                                })()}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PrinterInterface;
