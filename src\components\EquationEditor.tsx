import React, { useState } from 'react';
import { useLanguage } from '../i18n';

interface EquationEditorProps {
    onInsertEquation: (equation: any) => void;
}

const EquationEditor: React.FC<EquationEditorProps> = ({ onInsertEquation }) => {
    const { language, dir } = useLanguage();
    const [selectedCategory, setSelectedCategory] = useState<string>('basic');

    // Categories of equations
    const categories = [
        { id: 'basic', name: language === 'ar' ? 'المعادلات الأساسية' : 'Basic Equations' },
        { id: 'physics', name: language === 'ar' ? 'الفيزياء الطبية' : 'Medical Physics' },
        { id: 'biomedical', name: language === 'ar' ? 'الهندسة الطبية' : 'Biomedical Engineering' },
        { id: 'statistics', name: language === 'ar' ? 'الإحصاء الطبي' : 'Medical Statistics' },
        { id: 'signal', name: language === 'ar' ? 'معالجة الإشارات' : 'Signal Processing' }
    ];

    // Equations organized by category
    const equations = {
        basic: [
            {
                id: 'quadratic',
                name: language === 'ar' ? 'المعادلة التربيعية' : 'Quadratic Formula',
                latex: 'x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}',
                description: language === 'ar' 
                    ? 'حل المعادلة التربيعية ax² + bx + c = 0'
                    : 'Solution to quadratic equation ax² + bx + c = 0'
            },
            {
                id: 'exponential',
                name: language === 'ar' ? 'النمو الأسي' : 'Exponential Growth',
                latex: 'N(t) = N_0 e^{rt}',
                description: language === 'ar'
                    ? 'نموذج النمو الأسي حيث r هو معدل النمو'
                    : 'Exponential growth model where r is the growth rate'
            },
            {
                id: 'logarithm',
                name: language === 'ar' ? 'اللوغاريتم الطبيعي' : 'Natural Logarithm',
                latex: '\\ln(ab) = \\ln(a) + \\ln(b)',
                description: language === 'ar'
                    ? 'خاصية اللوغاريتم الطبيعي للضرب'
                    : 'Natural logarithm property for multiplication'
            }
        ],
        physics: [
            {
                id: 'ohms_law',
                name: language === 'ar' ? 'قانون أوم' : "Ohm's Law",
                latex: 'V = I \\times R',
                description: language === 'ar'
                    ? 'العلاقة بين الجهد والتيار والمقاومة'
                    : 'Relationship between voltage, current, and resistance'
            },
            {
                id: 'radioactive_decay',
                name: language === 'ar' ? 'التحلل الإشعاعي' : 'Radioactive Decay',
                latex: 'N(t) = N_0 e^{-\\lambda t}',
                description: language === 'ar'
                    ? 'قانون التحلل الإشعاعي مع الزمن'
                    : 'Radioactive decay law with time'
            },
            {
                id: 'beer_lambert',
                name: language === 'ar' ? 'قانون بير-لامبرت' : 'Beer-Lambert Law',
                latex: 'A = \\varepsilon c l',
                description: language === 'ar'
                    ? 'امتصاص الضوء في المواد'
                    : 'Light absorption in materials'
            },
            {
                id: 'planck_energy',
                name: language === 'ar' ? 'طاقة بلانك' : "Planck's Energy",
                latex: 'E = h\\nu = \\frac{hc}{\\lambda}',
                description: language === 'ar'
                    ? 'طاقة الفوتون حسب تردده أو طوله الموجي'
                    : 'Photon energy based on frequency or wavelength'
            }
        ],
        biomedical: [
            {
                id: 'cardiac_output',
                name: language === 'ar' ? 'النتاج القلبي' : 'Cardiac Output',
                latex: 'CO = HR \\times SV',
                description: language === 'ar'
                    ? 'حساب النتاج القلبي من معدل النبض وحجم الضربة'
                    : 'Cardiac output calculation from heart rate and stroke volume'
            },
            {
                id: 'blood_pressure',
                name: language === 'ar' ? 'متوسط الضغط الشرياني' : 'Mean Arterial Pressure',
                latex: 'MAP = DBP + \\frac{1}{3}(SBP - DBP)',
                description: language === 'ar'
                    ? 'حساب متوسط الضغط الشرياني'
                    : 'Mean arterial pressure calculation'
            },
            {
                id: 'bmi',
                name: language === 'ar' ? 'مؤشر كتلة الجسم' : 'Body Mass Index',
                latex: 'BMI = \\frac{\\text{Weight (kg)}}{\\text{Height (m)}^2}',
                description: language === 'ar'
                    ? 'حساب مؤشر كتلة الجسم'
                    : 'Body mass index calculation'
            },
            {
                id: 'clearance',
                name: language === 'ar' ? 'التصفية الكلوية' : 'Renal Clearance',
                latex: 'C = \\frac{U \\times V}{P}',
                description: language === 'ar'
                    ? 'حساب التصفية الكلوية للمواد'
                    : 'Renal clearance calculation for substances'
            }
        ],
        statistics: [
            {
                id: 'mean',
                name: language === 'ar' ? 'المتوسط الحسابي' : 'Arithmetic Mean',
                latex: '\\bar{x} = \\frac{1}{n} \\sum_{i=1}^{n} x_i',
                description: language === 'ar'
                    ? 'المتوسط الحسابي لمجموعة من القيم'
                    : 'Arithmetic mean of a set of values'
            },
            {
                id: 'standard_deviation',
                name: language === 'ar' ? 'الانحراف المعياري' : 'Standard Deviation',
                latex: 's = \\sqrt{\\frac{1}{n-1} \\sum_{i=1}^{n} (x_i - \\bar{x})^2}',
                description: language === 'ar'
                    ? 'مقياس تشتت البيانات حول المتوسط'
                    : 'Measure of data dispersion around the mean'
            },
            {
                id: 'correlation',
                name: language === 'ar' ? 'معامل الارتباط' : 'Correlation Coefficient',
                latex: 'r = \\frac{\\sum_{i=1}^{n} (x_i - \\bar{x})(y_i - \\bar{y})}{\\sqrt{\\sum_{i=1}^{n} (x_i - \\bar{x})^2 \\sum_{i=1}^{n} (y_i - \\bar{y})^2}}',
                description: language === 'ar'
                    ? 'قياس قوة العلاقة الخطية بين متغيرين'
                    : 'Measure of linear relationship strength between two variables'
            },
            {
                id: 'confidence_interval',
                name: language === 'ar' ? 'فترة الثقة' : 'Confidence Interval',
                latex: 'CI = \\bar{x} \\pm t_{\\alpha/2} \\frac{s}{\\sqrt{n}}',
                description: language === 'ar'
                    ? 'فترة الثقة للمتوسط الحسابي'
                    : 'Confidence interval for the mean'
            }
        ],
        signal: [
            {
                id: 'fourier_transform',
                name: language === 'ar' ? 'تحويل فورييه' : 'Fourier Transform',
                latex: 'F(\\omega) = \\int_{-\\infty}^{\\infty} f(t) e^{-i\\omega t} dt',
                description: language === 'ar'
                    ? 'تحويل الإشارة من المجال الزمني إلى الترددي'
                    : 'Transform signal from time domain to frequency domain'
            },
            {
                id: 'convolution',
                name: language === 'ar' ? 'التطبيق' : 'Convolution',
                latex: '(f * g)(t) = \\int_{-\\infty}^{\\infty} f(\\tau) g(t - \\tau) d\\tau',
                description: language === 'ar'
                    ? 'عملية التطبيق بين إشارتين'
                    : 'Convolution operation between two signals'
            },
            {
                id: 'sampling_theorem',
                name: language === 'ar' ? 'نظرية العينات' : 'Sampling Theorem',
                latex: 'f_s \\geq 2f_{max}',
                description: language === 'ar'
                    ? 'الحد الأدنى لتردد العينات لتجنب التداخل'
                    : 'Minimum sampling frequency to avoid aliasing'
            },
            {
                id: 'filter_response',
                name: language === 'ar' ? 'استجابة المرشح' : 'Filter Response',
                latex: 'H(\\omega) = \\frac{Y(\\omega)}{X(\\omega)}',
                description: language === 'ar'
                    ? 'دالة التحويل للمرشح في المجال الترددي'
                    : 'Filter transfer function in frequency domain'
            }
        ]
    };

    const handleInsertEquation = (equation: any) => {
        const equationHtml = `
            <div class="equation-block" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 4px;">
                <h4 style="margin: 0 0 10px 0; color: #007bff; font-size: 1.1em;">${equation.name}</h4>
                <div style="font-family: 'Times New Roman', serif; font-size: 20px; text-align: center; margin: 15px 0; font-weight: bold;">
                    ${equation.latex}
                </div>
                <p style="margin: 10px 0 0 0; font-style: italic; color: #666; font-size: 0.9em;">
                    ${equation.description}
                </p>
            </div>
        `;
        
        onInsertEquation({
            ...equation,
            html: equationHtml
        });
    };

    return (
        <div className="equation-editor bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="p-4 bg-purple-50 dark:bg-purple-900 border-b border-purple-200 dark:border-purple-800">
                <h2 className="text-xl font-bold text-purple-800 dark:text-purple-200">
                    {language === 'ar' ? 'محرر المعادلات الرياضية' : 'Mathematical Equation Editor'}
                </h2>
                <p className="text-sm text-purple-600 dark:text-purple-300 mt-1">
                    {language === 'ar' 
                        ? 'اختر المعادلة المناسبة لإدراجها في النص'
                        : 'Select the appropriate equation to insert into your text'
                    }
                </p>
            </div>

            {/* Categories Navigation */}
            <div className="flex overflow-x-auto p-2 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                {categories.map(category => (
                    <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`px-4 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ${
                            selectedCategory === category.id
                                ? 'bg-purple-600 text-white'
                                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                        }`}
                    >
                        {category.name}
                    </button>
                ))}
            </div>

            {/* Equations Grid */}
            <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {equations[selectedCategory as keyof typeof equations]?.map(equation => (
                    <div 
                        key={equation.id}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-purple-500 dark:hover:border-purple-400 transition-colors cursor-pointer"
                        onClick={() => handleInsertEquation(equation)}
                    >
                        <h3 className="font-bold text-gray-800 dark:text-white mb-2">{equation.name}</h3>
                        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded mb-3 text-center">
                            <div className="font-mono text-lg" style={{ fontFamily: 'Times New Roman, serif' }}>
                                {equation.latex}
                            </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{equation.description}</p>
                        <button className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-3 py-1 rounded-full">
                            {language === 'ar' ? 'إدراج المعادلة' : 'Insert Equation'}
                        </button>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default EquationEditor;
