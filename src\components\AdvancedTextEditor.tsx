import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../i18n';

interface AdvancedTextEditorProps {
    content: string;
    onChange: (content: string) => void;
    placeholder?: string;
}

const AdvancedTextEditor: React.FC<AdvancedTextEditorProps> = ({
    content,
    onChange,
    placeholder
}) => {
    const { language, dir } = useLanguage();
    const editorRef = useRef<HTMLDivElement>(null);
    const [showTemplates, setShowTemplates] = useState(false);
    const [showEquations, setShowEquations] = useState(false);
    const [selectedTemplate, setSelectedTemplate] = useState<string>('');

    // Physics and Biomedical Engineering Templates
    const templates = {
        physics: [
            {
                id: 'ohms_law',
                name: language === 'ar' ? 'قانون أوم' : "Ohm's Law",
                content: language === 'ar' 
                    ? '<h3>قانون أوم</h3><p><strong>القانون:</strong> V = I × R</p><p><strong>حيث:</strong></p><ul><li>V = الجهد (فولت)</li><li>I = التيار (أمبير)</li><li>R = المقاومة (أوم)</li></ul><p><strong>التطبيق في الهندسة الطبية الحيوية:</strong></p><p>يُستخدم قانون أوم في تصميم الدوائر الكهربائية للأجهزة الطبية مثل أجهزة تخطيط القلب وأجهزة التحفيز الكهربائي.</p>'
                    : '<h3>Ohm\'s Law</h3><p><strong>Law:</strong> V = I × R</p><p><strong>Where:</strong></p><ul><li>V = Voltage (Volts)</li><li>I = Current (Amperes)</li><li>R = Resistance (Ohms)</li></ul><p><strong>Application in Biomedical Engineering:</strong></p><p>Ohm\'s law is used in designing electrical circuits for medical devices such as ECG machines and electrical stimulation devices.</p>'
            },
            {
                id: 'radioactive_decay',
                name: language === 'ar' ? 'قانون التحلل الإشعاعي' : 'Radioactive Decay Law',
                content: language === 'ar'
                    ? '<h3>قانون التحلل الإشعاعي</h3><p><strong>القانون:</strong> N(t) = N₀ × e^(-λt)</p><p><strong>حيث:</strong></p><ul><li>N(t) = عدد النوى في الزمن t</li><li>N₀ = العدد الأولي للنوى</li><li>λ = ثابت التحلل</li><li>t = الزمن</li></ul><p><strong>التطبيق في الطب النووي:</strong></p><p>يُستخدم في حساب جرعات المواد المشعة المستخدمة في التشخيص والعلاج الطبي.</p>'
                    : '<h3>Radioactive Decay Law</h3><p><strong>Law:</strong> N(t) = N₀ × e^(-λt)</p><p><strong>Where:</strong></p><ul><li>N(t) = Number of nuclei at time t</li><li>N₀ = Initial number of nuclei</li><li>λ = Decay constant</li><li>t = Time</li></ul><p><strong>Application in Nuclear Medicine:</strong></p><p>Used in calculating doses of radioactive materials used in medical diagnosis and treatment.</p>'
            },
            {
                id: 'beer_lambert',
                name: language === 'ar' ? 'قانون بير-لامبرت' : 'Beer-Lambert Law',
                content: language === 'ar'
                    ? '<h3>قانون بير-لامبرت</h3><p><strong>القانون:</strong> A = ε × c × l</p><p><strong>حيث:</strong></p><ul><li>A = الامتصاص</li><li>ε = معامل الامتصاص المولي</li><li>c = التركيز</li><li>l = طول المسار الضوئي</li></ul><p><strong>التطبيق في التشخيص الطبي:</strong></p><p>يُستخدم في أجهزة قياس الأكسجين في الدم (Pulse Oximetry) وتحليل عينات الدم.</p>'
                    : '<h3>Beer-Lambert Law</h3><p><strong>Law:</strong> A = ε × c × l</p><p><strong>Where:</strong></p><ul><li>A = Absorbance</li><li>ε = Molar absorption coefficient</li><li>c = Concentration</li><li>l = Path length</li></ul><p><strong>Application in Medical Diagnosis:</strong></p><p>Used in pulse oximetry devices and blood sample analysis.</p>'
            }
        ],
        biomedical: [
            {
                id: 'cardiac_output',
                name: language === 'ar' ? 'النتاج القلبي' : 'Cardiac Output',
                content: language === 'ar'
                    ? '<h3>النتاج القلبي</h3><p><strong>المعادلة:</strong> CO = HR × SV</p><p><strong>حيث:</strong></p><ul><li>CO = النتاج القلبي (لتر/دقيقة)</li><li>HR = معدل ضربات القلب (ضربة/دقيقة)</li><li>SV = حجم الضربة (مل)</li></ul><p><strong>القيم الطبيعية:</strong></p><ul><li>البالغ الطبيعي: 4-8 لتر/دقيقة</li><li>معدل ضربات القلب: 60-100 ضربة/دقيقة</li><li>حجم الضربة: 60-100 مل</li></ul>'
                    : '<h3>Cardiac Output</h3><p><strong>Equation:</strong> CO = HR × SV</p><p><strong>Where:</strong></p><ul><li>CO = Cardiac Output (L/min)</li><li>HR = Heart Rate (beats/min)</li><li>SV = Stroke Volume (mL)</li></ul><p><strong>Normal Values:</strong></p><ul><li>Normal adult: 4-8 L/min</li><li>Heart rate: 60-100 beats/min</li><li>Stroke volume: 60-100 mL</li></ul>'
            },
            {
                id: 'blood_pressure',
                name: language === 'ar' ? 'ضغط الدم' : 'Blood Pressure',
                content: language === 'ar'
                    ? '<h3>ضغط الدم</h3><p><strong>المعادلة:</strong> MAP = DBP + 1/3(SBP - DBP)</p><p><strong>حيث:</strong></p><ul><li>MAP = متوسط الضغط الشرياني</li><li>DBP = الضغط الانبساطي</li><li>SBP = الضغط الانقباضي</li></ul><p><strong>التصنيف (mmHg):</strong></p><ul><li>طبيعي: أقل من 120/80</li><li>مرتفع قليلاً: 120-129/أقل من 80</li><li>المرحلة الأولى: 130-139/80-89</li><li>المرحلة الثانية: 140/90 أو أعلى</li></ul>'
                    : '<h3>Blood Pressure</h3><p><strong>Equation:</strong> MAP = DBP + 1/3(SBP - DBP)</p><p><strong>Where:</strong></p><ul><li>MAP = Mean Arterial Pressure</li><li>DBP = Diastolic Blood Pressure</li><li>SBP = Systolic Blood Pressure</li></ul><p><strong>Classification (mmHg):</strong></p><ul><li>Normal: Less than 120/80</li><li>Elevated: 120-129/Less than 80</li><li>Stage 1: 130-139/80-89</li><li>Stage 2: 140/90 or higher</li></ul>'
            },
            {
                id: 'bmi_calculation',
                name: language === 'ar' ? 'مؤشر كتلة الجسم' : 'Body Mass Index (BMI)',
                content: language === 'ar'
                    ? '<h3>مؤشر كتلة الجسم (BMI)</h3><p><strong>المعادلة:</strong> BMI = الوزن (كغ) / الطول² (م²)</p><p><strong>التصنيف:</strong></p><ul><li>نقص الوزن: أقل من 18.5</li><li>الوزن الطبيعي: 18.5-24.9</li><li>زيادة الوزن: 25-29.9</li><li>السمنة من الدرجة الأولى: 30-34.9</li><li>السمنة من الدرجة الثانية: 35-39.9</li><li>السمنة المفرطة: 40 أو أكثر</li></ul><p><strong>الأهمية الطبية:</strong></p><p>يُستخدم كمؤشر لتقييم المخاطر الصحية المرتبطة بالوزن.</p>'
                    : '<h3>Body Mass Index (BMI)</h3><p><strong>Equation:</strong> BMI = Weight (kg) / Height² (m²)</p><p><strong>Classification:</strong></p><ul><li>Underweight: Less than 18.5</li><li>Normal weight: 18.5-24.9</li><li>Overweight: 25-29.9</li><li>Obesity Class I: 30-34.9</li><li>Obesity Class II: 35-39.9</li><li>Obesity Class III: 40 or higher</li></ul><p><strong>Medical Importance:</strong></p><p>Used as an indicator to assess health risks associated with weight.</p>'
            }
        ],
        imaging: [
            {
                id: 'ct_basics',
                name: language === 'ar' ? 'أساسيات التصوير المقطعي' : 'CT Imaging Basics',
                content: language === 'ar'
                    ? '<h3>أساسيات التصوير المقطعي المحوسب (CT)</h3><p><strong>المبدأ الفيزيائي:</strong></p><p>يعتمد على قانون بير-لامبرت لامتصاص الأشعة السينية في الأنسجة المختلفة.</p><p><strong>وحدات هاونسفيلد (HU):</strong></p><ul><li>الهواء: -1000 HU</li><li>الدهون: -100 إلى -50 HU</li><li>الماء: 0 HU</li><li>العضلات: 10 إلى 40 HU</li><li>العظام: +400 إلى +1000 HU</li></ul><p><strong>معايير الجودة:</strong></p><ul><li>الدقة المكانية</li><li>التباين</li><li>الضوضاء</li><li>الجرعة الإشعاعية</li></ul>'
                    : '<h3>Computed Tomography (CT) Basics</h3><p><strong>Physical Principle:</strong></p><p>Based on Beer-Lambert law for X-ray absorption in different tissues.</p><p><strong>Hounsfield Units (HU):</strong></p><ul><li>Air: -1000 HU</li><li>Fat: -100 to -50 HU</li><li>Water: 0 HU</li><li>Muscle: 10 to 40 HU</li><li>Bone: +400 to +1000 HU</li></ul><p><strong>Quality Parameters:</strong></p><ul><li>Spatial resolution</li><li>Contrast</li><li>Noise</li><li>Radiation dose</li></ul>'
            }
        ]
    };

    // Mathematical equations for biomedical engineering
    const equations = [
        {
            id: 'fourier_transform',
            name: language === 'ar' ? 'تحويل فورييه' : 'Fourier Transform',
            latex: 'F(\\omega) = \\int_{-\\infty}^{\\infty} f(t) e^{-i\\omega t} dt',
            description: language === 'ar' 
                ? 'يُستخدم في تحليل الإشارات الطبية مثل تخطيط القلب والدماغ'
                : 'Used in medical signal analysis such as ECG and EEG'
        },
        {
            id: 'diffusion_equation',
            name: language === 'ar' ? 'معادلة الانتشار' : 'Diffusion Equation',
            latex: '\\frac{\\partial C}{\\partial t} = D \\nabla^2 C',
            description: language === 'ar'
                ? 'تصف انتشار الأدوية والأكسجين في الأنسجة'
                : 'Describes drug and oxygen diffusion in tissues'
        },
        {
            id: 'nernst_equation',
            name: language === 'ar' ? 'معادلة نيرنست' : 'Nernst Equation',
            latex: 'E = E^0 - \\frac{RT}{nF} \\ln\\left(\\frac{[Red]}{[Ox]}\\right)',
            description: language === 'ar'
                ? 'تحدد الجهد الكهربائي عبر أغشية الخلايا'
                : 'Determines electrical potential across cell membranes'
        }
    ];

    useEffect(() => {
        if (editorRef.current && content !== editorRef.current.innerHTML) {
            editorRef.current.innerHTML = content;
        }
    }, [content]);

    const handleInput = () => {
        if (editorRef.current) {
            onChange(editorRef.current.innerHTML);
        }
    };

    const executeCommand = (command: string, value?: string) => {
        document.execCommand(command, false, value);
        editorRef.current?.focus();
        handleInput();
    };

    const insertTemplate = (template: any) => {
        if (editorRef.current) {
            const selection = window.getSelection();
            if (selection && selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                range.deleteContents();
                const div = document.createElement('div');
                div.innerHTML = template.content;
                range.insertNode(div);
                selection.removeAllRanges();
            } else {
                editorRef.current.innerHTML += template.content;
            }
            handleInput();
            setShowTemplates(false);
        }
    };

    const insertEquation = (equation: any) => {
        if (editorRef.current) {
            const equationHtml = `
                <div class="equation-block" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 4px;">
                    <h4 style="margin: 0 0 10px 0; color: #007bff;">${equation.name}</h4>
                    <div style="font-family: 'Times New Roman', serif; font-size: 18px; text-align: center; margin: 15px 0;">
                        ${equation.latex}
                    </div>
                    <p style="margin: 10px 0 0 0; font-style: italic; color: #666;">
                        ${equation.description}
                    </p>
                </div>
            `;
            
            const selection = window.getSelection();
            if (selection && selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                range.deleteContents();
                const div = document.createElement('div');
                div.innerHTML = equationHtml;
                range.insertNode(div);
                selection.removeAllRanges();
            } else {
                editorRef.current.innerHTML += equationHtml;
            }
            handleInput();
            setShowEquations(false);
        }
    };

    return (
        <div className="advanced-text-editor border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
            {/* Toolbar */}
            <div className="toolbar bg-gray-50 dark:bg-gray-800 border-b border-gray-300 dark:border-gray-600 p-3">
                <div className="flex flex-wrap gap-2">
                    {/* Basic Formatting */}
                    <div className="flex gap-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button
                            onClick={() => executeCommand('bold')}
                            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                            title={language === 'ar' ? 'عريض' : 'Bold'}
                        >
                            <i className="fas fa-bold"></i>
                        </button>
                        <button
                            onClick={() => executeCommand('italic')}
                            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                            title={language === 'ar' ? 'مائل' : 'Italic'}
                        >
                            <i className="fas fa-italic"></i>
                        </button>
                        <button
                            onClick={() => executeCommand('underline')}
                            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                            title={language === 'ar' ? 'تحته خط' : 'Underline'}
                        >
                            <i className="fas fa-underline"></i>
                        </button>
                    </div>

                    {/* Alignment */}
                    <div className="flex gap-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button
                            onClick={() => executeCommand('justifyLeft')}
                            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                            title={language === 'ar' ? 'محاذاة يسار' : 'Align Left'}
                        >
                            <i className="fas fa-align-left"></i>
                        </button>
                        <button
                            onClick={() => executeCommand('justifyCenter')}
                            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                            title={language === 'ar' ? 'محاذاة وسط' : 'Align Center'}
                        >
                            <i className="fas fa-align-center"></i>
                        </button>
                        <button
                            onClick={() => executeCommand('justifyRight')}
                            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                            title={language === 'ar' ? 'محاذاة يمين' : 'Align Right'}
                        >
                            <i className="fas fa-align-right"></i>
                        </button>
                    </div>

                    {/* Lists */}
                    <div className="flex gap-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button
                            onClick={() => executeCommand('insertUnorderedList')}
                            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                            title={language === 'ar' ? 'قائمة نقطية' : 'Bullet List'}
                        >
                            <i className="fas fa-list-ul"></i>
                        </button>
                        <button
                            onClick={() => executeCommand('insertOrderedList')}
                            className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                            title={language === 'ar' ? 'قائمة مرقمة' : 'Numbered List'}
                        >
                            <i className="fas fa-list-ol"></i>
                        </button>
                    </div>

                    {/* Templates and Equations */}
                    <div className="flex gap-1">
                        <div className="relative">
                            <button
                                onClick={() => setShowTemplates(!showTemplates)}
                                className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded bg-blue-100 dark:bg-blue-900"
                                title={language === 'ar' ? 'قوالب الفيزياء والهندسة الطبية' : 'Physics & Biomedical Templates'}
                            >
                                <i className="fas fa-file-medical"></i>
                            </button>
                            
                            {showTemplates && (
                                <div className="absolute top-full left-0 mt-1 w-80 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                                    <div className="p-3">
                                        <h3 className="font-bold mb-3 text-gray-800 dark:text-white">
                                            {language === 'ar' ? 'قوالب الفيزياء والهندسة الطبية' : 'Physics & Biomedical Templates'}
                                        </h3>
                                        
                                        {/* Physics Templates */}
                                        <div className="mb-4">
                                            <h4 className="font-semibold text-sm text-blue-600 mb-2">
                                                {language === 'ar' ? 'الفيزياء الطبية' : 'Medical Physics'}
                                            </h4>
                                            {templates.physics.map(template => (
                                                <button
                                                    key={template.id}
                                                    onClick={() => insertTemplate(template)}
                                                    className="block w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm"
                                                >
                                                    {template.name}
                                                </button>
                                            ))}
                                        </div>

                                        {/* Biomedical Templates */}
                                        <div className="mb-4">
                                            <h4 className="font-semibold text-sm text-green-600 mb-2">
                                                {language === 'ar' ? 'الهندسة الطبية الحيوية' : 'Biomedical Engineering'}
                                            </h4>
                                            {templates.biomedical.map(template => (
                                                <button
                                                    key={template.id}
                                                    onClick={() => insertTemplate(template)}
                                                    className="block w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm"
                                                >
                                                    {template.name}
                                                </button>
                                            ))}
                                        </div>

                                        {/* Imaging Templates */}
                                        <div>
                                            <h4 className="font-semibold text-sm text-purple-600 mb-2">
                                                {language === 'ar' ? 'التصوير الطبي' : 'Medical Imaging'}
                                            </h4>
                                            {templates.imaging.map(template => (
                                                <button
                                                    key={template.id}
                                                    onClick={() => insertTemplate(template)}
                                                    className="block w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-sm"
                                                >
                                                    {template.name}
                                                </button>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        <div className="relative">
                            <button
                                onClick={() => setShowEquations(!showEquations)}
                                className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded bg-purple-100 dark:bg-purple-900"
                                title={language === 'ar' ? 'المعادلات الرياضية' : 'Mathematical Equations'}
                            >
                                <i className="fas fa-square-root-alt"></i>
                            </button>
                            
                            {showEquations && (
                                <div className="absolute top-full left-0 mt-1 w-80 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                                    <div className="p-3">
                                        <h3 className="font-bold mb-3 text-gray-800 dark:text-white">
                                            {language === 'ar' ? 'المعادلات الرياضية' : 'Mathematical Equations'}
                                        </h3>
                                        {equations.map(equation => (
                                            <button
                                                key={equation.id}
                                                onClick={() => insertEquation(equation)}
                                                className="block w-full text-left p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded mb-2"
                                            >
                                                <div className="font-semibold text-sm">{equation.name}</div>
                                                <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                                    {equation.description}
                                                </div>
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Editor Content */}
            <div
                ref={editorRef}
                contentEditable
                onInput={handleInput}
                className={`editor-content p-4 min-h-96 focus:outline-none ${dir === 'rtl' ? 'text-right' : 'text-left'}`}
                style={{ direction: dir }}
                dangerouslySetInnerHTML={{ __html: content }}
                data-placeholder={placeholder}
            />

            {/* Click outside to close dropdowns */}
            {(showTemplates || showEquations) && (
                <div
                    className="fixed inset-0 z-40"
                    onClick={() => {
                        setShowTemplates(false);
                        setShowEquations(false);
                    }}
                />
            )}
        </div>
    );
};

export default AdvancedTextEditor;
