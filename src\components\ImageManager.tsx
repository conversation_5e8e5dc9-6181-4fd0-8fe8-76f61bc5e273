import React, { useState, useRef } from 'react';
import { useLanguage } from '../i18n';

interface ImageManagerProps {
    onInsertImage: (imageHTML: string) => void;
    onClose: () => void;
}

const ImageManager: React.FC<ImageManagerProps> = ({ onInsertImage, onClose }) => {
    const { t } = useLanguage();
    const [imageUrl, setImageUrl] = useState('');
    const [altText, setAltText] = useState('');
    const [width, setWidth] = useState('auto');
    const [height, setHeight] = useState('auto');
    const [alignment, setAlignment] = useState<'left' | 'center' | 'right' | 'none'>('none');
    const [uploadedImage, setUploadedImage] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const result = e.target?.result as string;
                    setUploadedImage(result);
                    setImageUrl(result);
                    setAltText(file.name.split('.')[0]);
                };
                reader.readAsDataURL(file);
            } else {
                alert(t('pleaseSelectImageFile'));
            }
        }
    };

    const generateImageHTML = () => {
        if (!imageUrl) return '';

        let style = 'max-width: 100%; height: auto; margin: 1rem 0;';
        
        if (width !== 'auto') {
            style += ` width: ${width};`;
        }
        if (height !== 'auto') {
            style += ` height: ${height};`;
        }
        
        switch (alignment) {
            case 'left':
                style += ' float: left; margin-right: 1rem;';
                break;
            case 'right':
                style += ' float: right; margin-left: 1rem;';
                break;
            case 'center':
                style += ' display: block; margin-left: auto; margin-right: auto;';
                break;
        }

        return `<img src="${imageUrl}" alt="${altText}" style="${style}" />`;
    };

    const handleInsert = () => {
        if (!imageUrl) {
            alert(t('pleaseEnterImageUrl'));
            return;
        }

        const imageHTML = generateImageHTML();
        onInsertImage(imageHTML);
        onClose();
    };

    const previewHTML = generateImageHTML();

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('imageManager')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="space-y-6">
                    {/* Upload Section */}
                    <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('uploadImage')}</h4>
                        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                            <i className="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                {t('dragDropImage')}
                            </p>
                            <button
                                onClick={() => fileInputRef.current?.click()}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
                            >
                                {t('selectImage')}
                            </button>
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept="image/*"
                                onChange={handleFileUpload}
                                className="hidden"
                            />
                        </div>
                    </div>

                    {/* URL Section */}
                    <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('imageUrl')}</h4>
                        <input
                            type="url"
                            placeholder={t('enterImageUrl')}
                            value={imageUrl}
                            onChange={(e) => setImageUrl(e.target.value)}
                            className="w-full p-3 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                        />
                    </div>

                    {/* Image Settings */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium mb-2">{t('altText')}</label>
                            <input
                                type="text"
                                placeholder={t('imageDescription')}
                                value={altText}
                                onChange={(e) => setAltText(e.target.value)}
                                className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-2">{t('alignment')}</label>
                            <select
                                value={alignment}
                                onChange={(e) => setAlignment(e.target.value as 'left' | 'center' | 'right' | 'none')}
                                className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="none">{t('none')}</option>
                                <option value="left">{t('alignLeft')}</option>
                                <option value="center">{t('alignCenter')}</option>
                                <option value="right">{t('alignRight')}</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-2">{t('width')}</label>
                            <select
                                value={width}
                                onChange={(e) => setWidth(e.target.value)}
                                className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="auto">Auto</option>
                                <option value="25%">25%</option>
                                <option value="50%">50%</option>
                                <option value="75%">75%</option>
                                <option value="100%">100%</option>
                                <option value="200px">200px</option>
                                <option value="400px">400px</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium mb-2">{t('height')}</label>
                            <select
                                value={height}
                                onChange={(e) => setHeight(e.target.value)}
                                className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="auto">Auto</option>
                                <option value="150px">150px</option>
                                <option value="200px">200px</option>
                                <option value="300px">300px</option>
                                <option value="400px">400px</option>
                            </select>
                        </div>
                    </div>

                    {/* Preview */}
                    {imageUrl && (
                        <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('preview')}</h4>
                            <div className="border rounded p-4 bg-gray-50 dark:bg-gray-700">
                                <div dangerouslySetInnerHTML={{ __html: previewHTML }} />
                            </div>
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 rounded text-gray-600 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500"
                        >
                            {t('cancel')}
                        </button>
                        <button
                            onClick={handleInsert}
                            className="px-4 py-2 rounded text-white bg-blue-500 hover:bg-blue-600"
                            disabled={!imageUrl}
                        >
                            {t('insertImage')}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ImageManager;
