import React, { useState } from 'react';
import { useLanguage } from '../i18n';

interface BiomedicalTemplatesProps {
    onUseTemplate: (content: string) => void;
    onClose: () => void;
}

interface Template {
    id: string;
    name: string;
    nameAr: string;
    description: string;
    descriptionAr: string;
    content: string;
    category: string;
    categoryAr: string;
}

const BiomedicalTemplates: React.FC<BiomedicalTemplatesProps> = ({ onUseTemplate, onClose }) => {
    const { t, language } = useLanguage();
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

    const templates: Template[] = [
        {
            id: 'research-paper',
            name: 'Research Paper',
            nameAr: 'ورقة بحثية',
            description: 'Standard biomedical research paper template',
            descriptionAr: 'قالب ورقة بحثية في الهندسة الطبية الحيوية',
            category: 'Research',
            categoryAr: 'البحث',
            content: `
<h1>Title of Research Paper</h1>
<h2>Abstract</h2>
<p>Brief summary of the research objectives, methods, results, and conclusions...</p>

<h2>1. Introduction</h2>
<p>Background information and research objectives...</p>

<h2>2. Literature Review</h2>
<p>Review of existing research and theoretical framework...</p>

<h2>3. Methodology</h2>
<p>Description of research methods and experimental design...</p>

<h2>4. Results</h2>
<p>Presentation of findings and data analysis...</p>

<h2>5. Discussion</h2>
<p>Interpretation of results and implications...</p>

<h2>6. Conclusion</h2>
<p>Summary of key findings and future research directions...</p>

<h2>References</h2>
<p>List of cited sources...</p>
            `
        },
        {
            id: 'medical-device-design',
            name: 'Medical Device Design',
            nameAr: 'تصميم جهاز طبي',
            description: 'Template for medical device design documentation',
            descriptionAr: 'قالب لتوثيق تصميم الأجهزة الطبية',
            category: 'Design',
            categoryAr: 'التصميم',
            content: `
<h1>Medical Device Design Document</h1>

<h2>1. Device Overview</h2>
<p><strong>Device Name:</strong> [Device Name]</p>
<p><strong>Purpose:</strong> [Medical purpose and intended use]</p>
<p><strong>Target Users:</strong> [Healthcare professionals, patients, etc.]</p>

<h2>2. Design Requirements</h2>
<h3>2.1 Functional Requirements</h3>
<ul>
    <li>Primary functions</li>
    <li>Performance specifications</li>
    <li>User interface requirements</li>
</ul>

<h3>2.2 Safety Requirements</h3>
<ul>
    <li>Biocompatibility standards</li>
    <li>Electrical safety</li>
    <li>Risk management</li>
</ul>

<h2>3. Technical Specifications</h2>
<p>Detailed technical parameters and specifications...</p>

<h2>4. Testing and Validation</h2>
<p>Testing protocols and validation procedures...</p>

<h2>5. Regulatory Compliance</h2>
<p>FDA, CE marking, and other regulatory requirements...</p>
            `
        },
        {
            id: 'biomechanics-analysis',
            name: 'Biomechanics Analysis',
            nameAr: 'تحليل الميكانيكا الحيوية',
            description: 'Template for biomechanical analysis reports',
            descriptionAr: 'قالب لتقارير تحليل الميكانيكا الحيوية',
            category: 'Analysis',
            categoryAr: 'التحليل',
            content: `
<h1>Biomechanical Analysis Report</h1>

<h2>1. Introduction</h2>
<p>Overview of the biomechanical system under study...</p>

<h2>2. Anatomical Background</h2>
<p>Relevant anatomy and physiological considerations...</p>

<h2>3. Mechanical Properties</h2>
<table style="border-collapse: collapse; width: 100%; margin: 1rem 0;">
    <thead>
        <tr>
            <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6;">Material</th>
            <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6;">Young's Modulus (MPa)</th>
            <th style="border: 1px solid #d1d5db; padding: 8px; background-color: #f3f4f6;">Yield Strength (MPa)</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style="border: 1px solid #d1d5db; padding: 8px;">Cortical Bone</td>
            <td style="border: 1px solid #d1d5db; padding: 8px;">15,000-20,000</td>
            <td style="border: 1px solid #d1d5db; padding: 8px;">100-150</td>
        </tr>
    </tbody>
</table>

<h2>4. Force Analysis</h2>
<p>Analysis of forces acting on the biological system...</p>

<h2>5. Stress and Strain Distribution</h2>
<p>Computational analysis results...</p>

<h2>6. Clinical Implications</h2>
<p>Clinical relevance and applications...</p>
            `
        },
        {
            id: 'lab-report',
            name: 'Laboratory Report',
            nameAr: 'تقرير مختبر',
            description: 'Template for biomedical laboratory reports',
            descriptionAr: 'قالب لتقارير المختبرات الطبية الحيوية',
            category: 'Laboratory',
            categoryAr: 'المختبر',
            content: `
<h1>Laboratory Report</h1>

<h2>Experiment Information</h2>
<p><strong>Experiment Title:</strong> [Title]</p>
<p><strong>Date:</strong> [Date]</p>
<p><strong>Student Name:</strong> [Name]</p>
<p><strong>Lab Partner:</strong> [Partner Name]</p>

<h2>1. Objective</h2>
<p>State the purpose and objectives of the experiment...</p>

<h2>2. Theory</h2>
<p>Relevant theoretical background and principles...</p>

<h2>3. Materials and Equipment</h2>
<ul>
    <li>List of materials used</li>
    <li>Equipment specifications</li>
    <li>Safety considerations</li>
</ul>

<h2>4. Procedure</h2>
<ol>
    <li>Step-by-step experimental procedure</li>
    <li>Data collection methods</li>
    <li>Measurement techniques</li>
</ol>

<h2>5. Results</h2>
<p>Present experimental data, graphs, and observations...</p>

<h2>6. Analysis and Discussion</h2>
<p>Analyze results and discuss findings...</p>

<h2>7. Conclusion</h2>
<p>Summarize key findings and their significance...</p>

<h2>8. References</h2>
<p>Cite relevant sources and literature...</p>
            `
        }
    ];

    const categories = ['all', 'Research', 'Design', 'Analysis', 'Laboratory'];
    const categoriesAr = ['الكل', 'البحث', 'التصميم', 'التحليل', 'المختبر'];

    const filteredTemplates = selectedCategory === 'all' 
        ? templates 
        : templates.filter(template => template.category === selectedCategory);

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-6xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('biomedicalTemplate')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Categories and Templates List */}
                    <div className="lg:col-span-1">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('categories')}</h4>
                        <div className="space-y-2 mb-6">
                            {categories.map((category, index) => (
                                <button
                                    key={category}
                                    onClick={() => setSelectedCategory(category)}
                                    className={`w-full text-left p-2 rounded transition-colors ${
                                        selectedCategory === category
                                            ? 'bg-blue-500 text-white'
                                            : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'
                                    }`}
                                >
                                    {language === 'ar' ? categoriesAr[index] : category}
                                </button>
                            ))}
                        </div>

                        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('templates')}</h4>
                        <div className="space-y-2">
                            {filteredTemplates.map((template) => (
                                <button
                                    key={template.id}
                                    onClick={() => setSelectedTemplate(template)}
                                    className={`w-full text-left p-3 rounded border transition-colors ${
                                        selectedTemplate?.id === template.id
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                                    }`}
                                >
                                    <div className="font-medium">
                                        {language === 'ar' ? template.nameAr : template.name}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        {language === 'ar' ? template.descriptionAr : template.description}
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Template Preview */}
                    <div className="lg:col-span-2">
                        {selectedTemplate ? (
                            <div>
                                <div className="flex justify-between items-center mb-4">
                                    <h4 className="font-medium text-gray-900 dark:text-white">
                                        {t('preview')}: {language === 'ar' ? selectedTemplate.nameAr : selectedTemplate.name}
                                    </h4>
                                    <button
                                        onClick={() => {
                                            onUseTemplate(selectedTemplate.content);
                                            onClose();
                                        }}
                                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
                                    >
                                        {t('useTemplate')}
                                    </button>
                                </div>
                                <div 
                                    className="border rounded p-4 bg-gray-50 dark:bg-gray-700 overflow-auto max-h-96"
                                    dangerouslySetInnerHTML={{ __html: selectedTemplate.content }}
                                />
                            </div>
                        ) : (
                            <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                                <div className="text-center">
                                    <i className="fas fa-file-alt text-4xl mb-4"></i>
                                    <p>{t('selectTemplateToPreview')}</p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default BiomedicalTemplates;
