import React from 'react';
import { useLanguage } from '../i18n';

const Footer = () => {
  const { t, dir } = useLanguage();

  return (
    <footer className="bg-gray-800 dark:bg-gray-900 text-white py-8 mt-auto">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Author Information */}
          <div className="text-center md:text-left">
            <h3 className="text-lg font-bold mb-4 text-blue-400">
              {t('developedBy')}
            </h3>
            <div className="space-y-2">
              <p className="font-semibold text-xl">{t('authorInfo')}</p>
              <p className="text-gray-300">{t('authorAffiliation')}</p>
              <p className="text-sm text-gray-400">{t('copyright')}</p>
            </div>
          </div>

          {/* Contact Information */}
          <div className="text-center">
            <h3 className="text-lg font-bold mb-4 text-blue-400">
              <i className="fas fa-envelope mr-2"></i>
              {t('contactEmail').includes('@') ? 'Contact' : 'اتصل بنا'}
            </h3>
            <div className="space-y-2">
              <p className="text-gray-300">
                <i className="fas fa-envelope mr-2"></i>
                <a 
                  href={`mailto:${t('contactEmail')}`}
                  className="hover:text-blue-400 transition-colors"
                >
                  {t('contactEmail')}
                </a>
              </p>
              <p className="text-gray-300 text-sm">
                <i className="fas fa-phone mr-2"></i>
                {t('phoneNumbers')}
              </p>
            </div>
          </div>

          {/* Platform Information */}
          <div className="text-center md:text-right">
            <h3 className="text-lg font-bold mb-4 text-blue-400">
              <i className="fas fa-book-medical mr-2"></i>
              {t('appName')}
            </h3>
            <div className="space-y-2">
              <p className="text-gray-300 text-sm">
                {dir === 'rtl' 
                  ? 'منصة احترافية للكتب الإلكترونية في الهندسة الطبية الحيوية'
                  : 'Professional Biomedical Engineering E-Book Platform'
                }
              </p>
              <div className="flex justify-center md:justify-end space-x-4 mt-4">
                <a 
                  href="#" 
                  className="text-gray-400 hover:text-blue-400 transition-colors"
                  title="GitHub"
                >
                  <i className="fab fa-github text-xl"></i>
                </a>
                <a 
                  href="#" 
                  className="text-gray-400 hover:text-blue-400 transition-colors"
                  title="LinkedIn"
                >
                  <i className="fab fa-linkedin text-xl"></i>
                </a>
                <a 
                  href={`mailto:${t('contactEmail')}`}
                  className="text-gray-400 hover:text-blue-400 transition-colors"
                  title="Email"
                >
                  <i className="fas fa-envelope text-xl"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-6 text-center">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm mb-4 md:mb-0">
              {t('copyright')} - {t('authorInfo')}
            </p>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <span>
                <i className="fas fa-code mr-1"></i>
                {dir === 'rtl' ? 'مطور بـ' : 'Built with'} React + TypeScript
              </span>
              <span>
                <i className="fas fa-heart text-red-500 mr-1"></i>
                {dir === 'rtl' ? 'صنع بحب' : 'Made with love'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
