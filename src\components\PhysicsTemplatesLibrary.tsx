import React, { useState } from 'react';
import { useLanguage } from '../i18n';

interface PhysicsTemplatesLibraryProps {
    onSelectTemplate: (content: string) => void;
}

const PhysicsTemplatesLibrary: React.FC<PhysicsTemplatesLibraryProps> = ({ onSelectTemplate }) => {
    const { language, dir } = useLanguage();
    const [activeCategory, setActiveCategory] = useState<string>('physics');

    // Categories of templates
    const categories = [
        { id: 'physics', name: language === 'ar' ? 'الفيزياء الطبية' : 'Medical Physics' },
        { id: 'biomedical', name: language === 'ar' ? 'الهندسة الطبية الحيوية' : 'Biomedical Engineering' },
        { id: 'imaging', name: language === 'ar' ? 'التصوير الطبي' : 'Medical Imaging' },
        { id: 'radiation', name: language === 'ar' ? 'الإشعاع والوقاية' : 'Radiation & Protection' },
        { id: 'equipment', name: language === 'ar' ? 'الأجهزة الطبية' : 'Medical Equipment' }
    ];

    // Templates organized by category
    const templates = {
        physics: [
            {
                id: 'ohms_law',
                name: language === 'ar' ? 'قانون أوم' : "Ohm's Law",
                description: language === 'ar'
                    ? 'العلاقة بين الجهد والتيار والمقاومة في الدوائر الكهربائية'
                    : 'The relationship between voltage, current, and resistance in electrical circuits',
                content: language === 'ar'
                    ? '<h2>قانون أوم</h2><p><strong>القانون:</strong> V = I × R</p><p><strong>حيث:</strong></p><ul><li>V = الجهد (فولت)</li><li>I = التيار (أمبير)</li><li>R = المقاومة (أوم)</li></ul><p><strong>التطبيق في الهندسة الطبية الحيوية:</strong></p><p>يُستخدم قانون أوم في تصميم الدوائر الكهربائية للأجهزة الطبية مثل أجهزة تخطيط القلب وأجهزة التحفيز الكهربائي.</p>'
                    : '<h2>Ohm\'s Law</h2><p><strong>Law:</strong> V = I × R</p><p><strong>Where:</strong></p><ul><li>V = Voltage (Volts)</li><li>I = Current (Amperes)</li><li>R = Resistance (Ohms)</li></ul><p><strong>Application in Biomedical Engineering:</strong></p><p>Ohm\'s law is used in designing electrical circuits for medical devices such as ECG machines and electrical stimulation devices.</p>'
            },
            {
                id: 'radioactive_decay',
                name: language === 'ar' ? 'قانون التحلل الإشعاعي' : 'Radioactive Decay Law',
                description: language === 'ar'
                    ? 'معادلة تصف كيفية تناقص المواد المشعة مع مرور الوقت'
                    : 'Equation describing how radioactive materials decrease over time',
                content: language === 'ar'
                    ? '<h2>قانون التحلل الإشعاعي</h2><p><strong>القانون:</strong> N(t) = N₀ × e^(-λt)</p><p><strong>حيث:</strong></p><ul><li>N(t) = عدد النوى في الزمن t</li><li>N₀ = العدد الأولي للنوى</li><li>λ = ثابت التحلل</li><li>t = الزمن</li></ul><p><strong>التطبيق في الطب النووي:</strong></p><p>يُستخدم في حساب جرعات المواد المشعة المستخدمة في التشخيص والعلاج الطبي.</p><p><strong>عمر النصف:</strong></p><p>T₁/₂ = ln(2)/λ</p><p>يمثل الوقت اللازم لتحلل نصف المادة المشعة.</p>'
                    : '<h2>Radioactive Decay Law</h2><p><strong>Law:</strong> N(t) = N₀ × e^(-λt)</p><p><strong>Where:</strong></p><ul><li>N(t) = Number of nuclei at time t</li><li>N₀ = Initial number of nuclei</li><li>λ = Decay constant</li><li>t = Time</li></ul><p><strong>Application in Nuclear Medicine:</strong></p><p>Used in calculating doses of radioactive materials used in medical diagnosis and treatment.</p><p><strong>Half-life:</strong></p><p>T₁/₂ = ln(2)/λ</p><p>Represents the time required for half of the radioactive material to decay.</p>'
            },
            {
                id: 'beer_lambert',
                name: language === 'ar' ? 'قانون بير-لامبرت' : 'Beer-Lambert Law',
                description: language === 'ar'
                    ? 'قانون يصف امتصاص الضوء عند مروره خلال مادة'
                    : 'Law describing light absorption as it passes through a material',
                content: language === 'ar'
                    ? '<h2>قانون بير-لامبرت</h2><p><strong>القانون:</strong> A = ε × c × l</p><p><strong>حيث:</strong></p><ul><li>A = الامتصاص</li><li>ε = معامل الامتصاص المولي</li><li>c = التركيز</li><li>l = طول المسار الضوئي</li></ul><p><strong>التطبيق في التشخيص الطبي:</strong></p><p>يُستخدم في أجهزة قياس الأكسجين في الدم (Pulse Oximetry) وتحليل عينات الدم.</p><p><strong>النفاذية:</strong></p><p>T = I/I₀ = 10^(-A)</p><p>حيث I هي شدة الضوء المنتقل و I₀ هي شدة الضوء الساقط.</p>'
                    : '<h2>Beer-Lambert Law</h2><p><strong>Law:</strong> A = ε × c × l</p><p><strong>Where:</strong></p><ul><li>A = Absorbance</li><li>ε = Molar absorption coefficient</li><li>c = Concentration</li><li>l = Path length</li></ul><p><strong>Application in Medical Diagnosis:</strong></p><p>Used in pulse oximetry devices and blood sample analysis.</p><p><strong>Transmittance:</strong></p><p>T = I/I₀ = 10^(-A)</p><p>Where I is the transmitted light intensity and I₀ is the incident light intensity.</p>'
            }
        ],
        biomedical: [
            {
                id: 'cardiac_output',
                name: language === 'ar' ? 'النتاج القلبي' : 'Cardiac Output',
                description: language === 'ar'
                    ? 'حساب كمية الدم التي يضخها القلب في الدقيقة الواحدة'
                    : 'Calculation of blood volume pumped by the heart per minute',
                content: language === 'ar'
                    ? '<h2>النتاج القلبي</h2><p><strong>المعادلة:</strong> CO = HR × SV</p><p><strong>حيث:</strong></p><ul><li>CO = النتاج القلبي (لتر/دقيقة)</li><li>HR = معدل ضربات القلب (ضربة/دقيقة)</li><li>SV = حجم الضربة (مل)</li></ul><p><strong>القيم الطبيعية:</strong></p><ul><li>البالغ الطبيعي: 4-8 لتر/دقيقة</li><li>معدل ضربات القلب: 60-100 ضربة/دقيقة</li><li>حجم الضربة: 60-100 مل</li></ul><p><strong>طرق القياس:</strong></p><ul><li>تخفيف الصبغة</li><li>قياس فيك للأكسجين</li><li>تخطيط صدى القلب</li><li>قياس المعاوقة الكهربائية الصدرية</li></ul>'
                    : '<h2>Cardiac Output</h2><p><strong>Equation:</strong> CO = HR × SV</p><p><strong>Where:</strong></p><ul><li>CO = Cardiac Output (L/min)</li><li>HR = Heart Rate (beats/min)</li><li>SV = Stroke Volume (mL)</li></ul><p><strong>Normal Values:</strong></p><ul><li>Normal adult: 4-8 L/min</li><li>Heart rate: 60-100 beats/min</li><li>Stroke volume: 60-100 mL</li></ul><p><strong>Measurement Methods:</strong></p><ul><li>Dye dilution</li><li>Fick oxygen method</li><li>Echocardiography</li><li>Thoracic electrical bioimpedance</li></ul>'
            },
            {
                id: 'blood_pressure',
                name: language === 'ar' ? 'ضغط الدم' : 'Blood Pressure',
                description: language === 'ar'
                    ? 'قياس وتحليل ضغط الدم في الشرايين'
                    : 'Measurement and analysis of blood pressure in arteries',
                content: language === 'ar'
                    ? '<h2>ضغط الدم</h2><p><strong>المعادلة:</strong> MAP = DBP + 1/3(SBP - DBP)</p><p><strong>حيث:</strong></p><ul><li>MAP = متوسط الضغط الشرياني</li><li>DBP = الضغط الانبساطي</li><li>SBP = الضغط الانقباضي</li></ul><p><strong>التصنيف (mmHg):</strong></p><ul><li>طبيعي: أقل من 120/80</li><li>مرتفع قليلاً: 120-129/أقل من 80</li><li>المرحلة الأولى: 130-139/80-89</li><li>المرحلة الثانية: 140/90 أو أعلى</li></ul><p><strong>العوامل المؤثرة:</strong></p><ul><li>مقاومة الأوعية الدموية الطرفية</li><li>النتاج القلبي</li><li>لزوجة الدم</li><li>مرونة الشرايين</li></ul>'
                    : '<h2>Blood Pressure</h2><p><strong>Equation:</strong> MAP = DBP + 1/3(SBP - DBP)</p><p><strong>Where:</strong></p><ul><li>MAP = Mean Arterial Pressure</li><li>DBP = Diastolic Blood Pressure</li><li>SBP = Systolic Blood Pressure</li></ul><p><strong>Classification (mmHg):</strong></p><ul><li>Normal: Less than 120/80</li><li>Elevated: 120-129/Less than 80</li><li>Stage 1: 130-139/80-89</li><li>Stage 2: 140/90 or higher</li></ul><p><strong>Influencing Factors:</strong></p><ul><li>Peripheral vascular resistance</li><li>Cardiac output</li><li>Blood viscosity</li><li>Arterial compliance</li></ul>'
            }
        ],
        imaging: [
            {
                id: 'ct_basics',
                name: language === 'ar' ? 'أساسيات التصوير المقطعي' : 'CT Imaging Basics',
                description: language === 'ar'
                    ? 'مبادئ التصوير المقطعي المحوسب وتطبيقاته'
                    : 'Principles of computed tomography imaging and its applications',
                content: language === 'ar'
                    ? '<h2>أساسيات التصوير المقطعي المحوسب (CT)</h2><p><strong>المبدأ الفيزيائي:</strong></p><p>يعتمد على قانون بير-لامبرت لامتصاص الأشعة السينية في الأنسجة المختلفة.</p><p><strong>وحدات هاونسفيلد (HU):</strong></p><ul><li>الهواء: -1000 HU</li><li>الدهون: -100 إلى -50 HU</li><li>الماء: 0 HU</li><li>العضلات: 10 إلى 40 HU</li><li>العظام: +400 إلى +1000 HU</li></ul><p><strong>معايير الجودة:</strong></p><ul><li>الدقة المكانية</li><li>التباين</li><li>الضوضاء</li><li>الجرعة الإشعاعية</li></ul><p><strong>أنواع أجهزة CT:</strong></p><ul><li>CT التقليدي</li><li>CT الحلزوني</li><li>CT متعدد الشرائح</li><li>CT ثنائي الطاقة</li></ul>'
                    : '<h2>Computed Tomography (CT) Basics</h2><p><strong>Physical Principle:</strong></p><p>Based on Beer-Lambert law for X-ray absorption in different tissues.</p><p><strong>Hounsfield Units (HU):</strong></p><ul><li>Air: -1000 HU</li><li>Fat: -100 to -50 HU</li><li>Water: 0 HU</li><li>Muscle: 10 to 40 HU</li><li>Bone: +400 to +1000 HU</li></ul><p><strong>Quality Parameters:</strong></p><ul><li>Spatial resolution</li><li>Contrast</li><li>Noise</li><li>Radiation dose</li></ul><p><strong>CT Scanner Types:</strong></p><ul><li>Conventional CT</li><li>Spiral CT</li><li>Multi-slice CT</li><li>Dual-energy CT</li></ul>'
            },
            {
                id: 'mri_basics',
                name: language === 'ar' ? 'أساسيات التصوير بالرنين المغناطيسي' : 'MRI Basics',
                description: language === 'ar'
                    ? 'مبادئ التصوير بالرنين المغناطيسي وتطبيقاته'
                    : 'Principles of magnetic resonance imaging and its applications',
                content: language === 'ar'
                    ? '<h2>أساسيات التصوير بالرنين المغناطيسي (MRI)</h2><p><strong>المبدأ الفيزيائي:</strong></p><p>يعتمد على ظاهرة الرنين المغناطيسي النووي لذرات الهيدروجين في الأنسجة.</p><p><strong>المعلمات الأساسية:</strong></p><ul><li>زمن الاسترخاء الطولي (T1)</li><li>زمن الاسترخاء العرضي (T2)</li><li>كثافة البروتون (PD)</li></ul><p><strong>أنواع التباين:</strong></p><ul><li>صور T1-weighted</li><li>صور T2-weighted</li><li>صور PD-weighted</li><li>صور FLAIR</li><li>صور انتشار (DWI)</li></ul><p><strong>مزايا MRI:</strong></p><ul><li>لا يستخدم إشعاعاً مؤيناً</li><li>تباين ممتاز للأنسجة الرخوة</li><li>قدرة على التصوير متعدد المستويات</li><li>تصوير وظيفي</li></ul>'
                    : '<h2>Magnetic Resonance Imaging (MRI) Basics</h2><p><strong>Physical Principle:</strong></p><p>Based on nuclear magnetic resonance phenomenon of hydrogen atoms in tissues.</p><p><strong>Basic Parameters:</strong></p><ul><li>Longitudinal relaxation time (T1)</li><li>Transverse relaxation time (T2)</li><li>Proton density (PD)</li></ul><p><strong>Contrast Types:</strong></p><ul><li>T1-weighted images</li><li>T2-weighted images</li><li>PD-weighted images</li><li>FLAIR images</li><li>Diffusion-weighted images (DWI)</li></ul><p><strong>MRI Advantages:</strong></p><ul><li>No ionizing radiation</li><li>Excellent soft tissue contrast</li><li>Multiplanar imaging capability</li><li>Functional imaging</li></ul>'
            }
        ],
        radiation: [
            {
                id: 'radiation_dose',
                name: language === 'ar' ? 'قياس الجرعات الإشعاعية' : 'Radiation Dosimetry',
                description: language === 'ar'
                    ? 'قياس وتقييم الجرعات الإشعاعية في التطبيقات الطبية'
                    : 'Measurement and evaluation of radiation doses in medical applications',
                content: language === 'ar'
                    ? '<h2>قياس الجرعات الإشعاعية</h2><p><strong>وحدات قياس الإشعاع:</strong></p><ul><li>الجرعة الممتصة: Gray (Gy) = 1 J/kg</li><li>الجرعة المكافئة: Sievert (Sv) = Gy × WR</li><li>الجرعة الفعالة: Sv = Σ(WT × HT)</li></ul><p><strong>حيث:</strong></p><ul><li>WR = معامل الترجيح الإشعاعي</li><li>WT = معامل الترجيح النسيجي</li><li>HT = الجرعة المكافئة للنسيج</li></ul><p><strong>حدود الجرعات:</strong></p><ul><li>العاملون في المجال الإشعاعي: 20 mSv/سنة</li><li>عامة الناس: 1 mSv/سنة</li></ul><p><strong>أجهزة قياس الإشعاع:</strong></p><ul><li>غرفة التأين</li><li>عداد جايجر-مولر</li><li>مقياس الوميض</li><li>مقياس الجرعات الحراري الضوئي (TLD)</li></ul>'
                    : '<h2>Radiation Dosimetry</h2><p><strong>Radiation Measurement Units:</strong></p><ul><li>Absorbed dose: Gray (Gy) = 1 J/kg</li><li>Equivalent dose: Sievert (Sv) = Gy × WR</li><li>Effective dose: Sv = Σ(WT × HT)</li></ul><p><strong>Where:</strong></p><ul><li>WR = Radiation weighting factor</li><li>WT = Tissue weighting factor</li><li>HT = Equivalent dose to tissue</li></ul><p><strong>Dose Limits:</strong></p><ul><li>Radiation workers: 20 mSv/year</li><li>General public: 1 mSv/year</li></ul><p><strong>Radiation Measuring Devices:</strong></p><ul><li>Ionization chamber</li><li>Geiger-Müller counter</li><li>Scintillation detector</li><li>Thermoluminescent dosimeter (TLD)</li></ul>'
            }
        ],
        equipment: [
            {
                id: 'ecg_principles',
                name: language === 'ar' ? 'مبادئ تخطيط القلب الكهربائي' : 'ECG Principles',
                description: language === 'ar'
                    ? 'أساسيات تخطيط كهربية القلب وتفسير النتائج'
                    : 'Basics of electrocardiography and interpretation of results',
                content: language === 'ar'
                    ? '<h2>مبادئ تخطيط القلب الكهربائي (ECG)</h2><p><strong>المبدأ الفيزيائي:</strong></p><p>قياس النشاط الكهربائي للقلب من خلال أقطاب كهربائية موضوعة على سطح الجسم.</p><p><strong>توصيلات ECG القياسية:</strong></p><ul><li>التوصيلات القياسية (I, II, III)</li><li>التوصيلات المعززة للأطراف (aVR, aVL, aVF)</li><li>التوصيلات الصدرية (V1-V6)</li></ul><p><strong>موجات ECG الطبيعية:</strong></p><ul><li>موجة P: انقباض الأذينين</li><li>مركب QRS: انقباض البطينين</li><li>موجة T: استرخاء البطينين</li></ul><p><strong>الفترات الزمنية الطبيعية:</strong></p><ul><li>فترة PR: 120-200 مللي ثانية</li><li>مدة QRS: 80-120 مللي ثانية</li><li>فترة QT: 350-440 مللي ثانية</li></ul>'
                    : '<h2>Electrocardiography (ECG) Principles</h2><p><strong>Physical Principle:</strong></p><p>Measuring the electrical activity of the heart through electrodes placed on the body surface.</p><p><strong>Standard ECG Leads:</strong></p><ul><li>Standard limb leads (I, II, III)</li><li>Augmented limb leads (aVR, aVL, aVF)</li><li>Precordial leads (V1-V6)</li></ul><p><strong>Normal ECG Waves:</strong></p><ul><li>P wave: Atrial depolarization</li><li>QRS complex: Ventricular depolarization</li><li>T wave: Ventricular repolarization</li></ul><p><strong>Normal Time Intervals:</strong></p><ul><li>PR interval: 120-200 ms</li><li>QRS duration: 80-120 ms</li><li>QT interval: 350-440 ms</li></ul>'
            }
        ]
    };

    return (
        <div className="physics-templates-library bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="p-4 bg-blue-50 dark:bg-blue-900 border-b border-blue-200 dark:border-blue-800">
                <h2 className="text-xl font-bold text-blue-800 dark:text-blue-200">
                    {language === 'ar' ? 'مكتبة قوالب الفيزياء والهندسة الطبية الحيوية' : 'Physics & Biomedical Engineering Templates Library'}
                </h2>
                <p className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                    {language === 'ar'
                        ? 'اختر من مجموعة متنوعة من القوالب المتخصصة لتسريع عملية الكتابة'
                        : 'Choose from a variety of specialized templates to speed up your writing process'
                    }
                </p>
            </div>

            {/* Categories Navigation */}
            <div className="flex overflow-x-auto p-2 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                {categories.map(category => (
                    <button
                        key={category.id}
                        onClick={() => setActiveCategory(category.id)}
                        className={`px-4 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ${
                            activeCategory === category.id
                                ? 'bg-blue-600 text-white'
                                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                        }`}
                    >
                        {category.name}
                    </button>
                ))}
            </div>

            {/* Templates Grid */}
            <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {templates[activeCategory as keyof typeof templates]?.map(template => (
                    <div
                        key={template.id}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-500 dark:hover:border-blue-400 transition-colors cursor-pointer"
                        onClick={() => onSelectTemplate(template.content)}
                    >
                        <h3 className="font-bold text-gray-800 dark:text-white mb-2">{template.name}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{template.description}</p>
                        <button className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full">
                            {language === 'ar' ? 'استخدم هذا القالب' : 'Use this template'}
                        </button>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default PhysicsTemplatesLibrary;