
import React from 'react';
import { NavLink } from 'react-router-dom';
import { useLanguage } from '../i18n';

const Header = () => {
  const { language, changeLanguage, t, dir } = useLanguage();
  const activeLinkClass = "bg-blue-600 text-white";
  const inactiveLinkClass = "text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700";

  return (
    <header className="bg-white dark:bg-gray-800 shadow-md">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <i className="fas fa-book-medical text-3xl text-blue-500 me-3"></i>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">{t('appName')}</h1>
          </div>
          <div className="flex items-center">
             <div className="language-switcher flex items-center border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
                <button
                  onClick={() => changeLanguage('en')}
                  className={`btn-language px-4 py-2 text-sm font-medium rounded-s-md transition-all duration-300 ${language === 'en' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'} ${language === 'en' ? 'active' : ''}`}
                >
                  <span className="flex items-center gap-1">
                    <i className="fas fa-globe-americas text-xs"></i>
                    EN
                  </span>
                </button>
                <button
                  onClick={() => changeLanguage('ar')}
                  className={`btn-language px-4 py-2 text-sm font-medium rounded-e-md transition-all duration-300 ${language === 'ar' ? 'bg-blue-600 text-white shadow-md' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'} ${language === 'ar' ? 'active' : ''}`}
                >
                  <span className="flex items-center gap-1">
                    <i className="fas fa-globe text-xs"></i>
                    عر
                  </span>
                </button>
              </div>
            <nav className="hidden md:flex items-center space-x-2 ms-4">
              <NavLink
                to="/"
                className={({ isActive }) => `${isActive ? activeLinkClass : inactiveLinkClass} px-3 py-2 rounded-md text-sm font-medium transition-colors`}>
                <i className={`fas fa-home ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>{t('home')}
              </NavLink>
              <NavLink
                to="/library"
                className={({ isActive }) => `${isActive ? activeLinkClass : inactiveLinkClass} px-3 py-2 rounded-md text-sm font-medium transition-colors`}>
                <i className={`fas fa-swatchbook ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>{t('myLibrary')}
              </NavLink>
              <NavLink
                to="/discovery"
                className={({ isActive }) => `${isActive ? activeLinkClass : inactiveLinkClass} px-3 py-2 rounded-md text-sm font-medium transition-colors`}>
                <i className={`fas fa-search ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>{t('discovery')}
              </NavLink>
              <NavLink
                to="/advanced-editor"
                className={({ isActive }) => `${isActive ? activeLinkClass : inactiveLinkClass} px-3 py-2 rounded-md text-sm font-medium transition-colors`}>
                <i className={`fas fa-edit ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>{t('advancedEditor')}
              </NavLink>
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;