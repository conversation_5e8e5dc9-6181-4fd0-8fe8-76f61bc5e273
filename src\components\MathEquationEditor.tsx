import React, { useState } from 'react';
import { useLanguage } from '../i18n';

interface MathEquationEditorProps {
    onInsertEquation: (equationHTML: string) => void;
    onClose: () => void;
}

const MathEquationEditor: React.FC<MathEquationEditorProps> = ({ onInsertEquation, onClose }) => {
    const { t, language } = useLanguage();
    const [selectedCategory, setSelectedCategory] = useState<string>('basic');
    const [customLatex, setCustomLatex] = useState<string>('');
    const [selectedEquation, setSelectedEquation] = useState<string>('');

    const equationCategories = {
        basic: {
            name: 'Basic Math',
            nameAr: 'الرياضيات الأساسية',
            equations: [
                { latex: 'x^2 + y^2 = z^2', description: 'Pythagorean theorem', descriptionAr: 'نظرية فيثاغورس' },
                { latex: 'E = mc^2', description: '<PERSON>\'s mass-energy equivalence', descriptionAr: 'معادلة أينشتاين للطاقة والكتلة' },
                { latex: '\\frac{a}{b} = \\frac{c}{d}', description: 'Proportion', descriptionAr: 'التناسب' },
                { latex: '\\sqrt{a^2 + b^2}', description: 'Square root', descriptionAr: 'الجذر التربيعي' },
                { latex: '\\sum_{i=1}^{n} x_i', description: 'Summation', descriptionAr: 'المجموع' },
                { latex: '\\int_{a}^{b} f(x) dx', description: 'Definite integral', descriptionAr: 'التكامل المحدود' }
            ]
        },
        biomedical: {
            name: 'Biomedical Engineering',
            nameAr: 'الهندسة الطبية الحيوية',
            equations: [
                { latex: 'Q = A \\cdot v', description: 'Flow rate equation', descriptionAr: 'معادلة معدل التدفق' },
                { latex: 'P = \\frac{F}{A}', description: 'Pressure equation', descriptionAr: 'معادلة الضغط' },
                { latex: 'R = \\frac{\\Delta P}{Q}', description: 'Resistance equation', descriptionAr: 'معادلة المقاومة' },
                { latex: 'C = \\frac{\\Delta V}{\\Delta P}', description: 'Compliance equation', descriptionAr: 'معادلة المرونة' },
                { latex: 'f = \\frac{1}{T}', description: 'Frequency equation', descriptionAr: 'معادلة التردد' },
                { latex: '\\sigma = \\frac{F}{A}', description: 'Stress equation', descriptionAr: 'معادلة الإجهاد' },
                { latex: '\\epsilon = \\frac{\\Delta L}{L_0}', description: 'Strain equation', descriptionAr: 'معادلة الانفعال' },
                { latex: 'E = \\frac{\\sigma}{\\epsilon}', description: 'Young\'s modulus', descriptionAr: 'معامل يونغ' }
            ]
        },
        physics: {
            name: 'Physics',
            nameAr: 'الفيزياء',
            equations: [
                { latex: 'F = ma', description: 'Newton\'s second law', descriptionAr: 'قانون نيوتن الثاني' },
                { latex: 'v = u + at', description: 'Kinematic equation', descriptionAr: 'معادلة الحركة' },
                { latex: 's = ut + \\frac{1}{2}at^2', description: 'Displacement equation', descriptionAr: 'معادلة الإزاحة' },
                { latex: 'P = \\frac{W}{t}', description: 'Power equation', descriptionAr: 'معادلة القدرة' },
                { latex: 'W = F \\cdot d \\cdot \\cos(\\theta)', description: 'Work equation', descriptionAr: 'معادلة الشغل' },
                { latex: 'KE = \\frac{1}{2}mv^2', description: 'Kinetic energy', descriptionAr: 'الطاقة الحركية' }
            ]
        },
        chemistry: {
            name: 'Chemistry',
            nameAr: 'الكيمياء',
            equations: [
                { latex: 'PV = nRT', description: 'Ideal gas law', descriptionAr: 'قانون الغاز المثالي' },
                { latex: 'pH = -\\log[H^+]', description: 'pH equation', descriptionAr: 'معادلة الأس الهيدروجيني' },
                { latex: 'C_1V_1 = C_2V_2', description: 'Dilution equation', descriptionAr: 'معادلة التخفيف' },
                { latex: 'K_a = \\frac{[H^+][A^-]}{[HA]}', description: 'Acid dissociation constant', descriptionAr: 'ثابت تفكك الحمض' },
                { latex: '\\Delta G = \\Delta H - T\\Delta S', description: 'Gibbs free energy', descriptionAr: 'طاقة جيبس الحرة' }
            ]
        }
    };

    const renderEquation = (latex: string) => {
        // Simple LaTeX to HTML conversion for preview
        let html = latex
            .replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '<span class="fraction"><span class="numerator">$1</span><span class="denominator">$2</span></span>')
            .replace(/\\sqrt\{([^}]+)\}/g, '√($1)')
            .replace(/\\sum_\{([^}]+)\}\^\{([^}]+)\}/g, '∑<sub>$1</sub><sup>$2</sup>')
            .replace(/\\int_\{([^}]+)\}\^\{([^}]+)\}/g, '∫<sub>$1</sub><sup>$2</sup>')
            .replace(/\\cdot/g, '·')
            .replace(/\\Delta/g, 'Δ')
            .replace(/\\theta/g, 'θ')
            .replace(/\\log/g, 'log')
            .replace(/\^([^{}\s]+)/g, '<sup>$1</sup>')
            .replace(/\^{([^}]+)}/g, '<sup>$1</sup>')
            .replace(/_([^{}\s]+)/g, '<sub>$1</sub>')
            .replace(/_{([^}]+)}/g, '<sub>$1</sub>');
        
        return html;
    };

    const generateEquationHTML = (latex: string) => {
        const renderedEquation = renderEquation(latex);
        return `<div class="math-equation" style="margin: 1rem 0; padding: 0.5rem; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em;" data-latex="${latex}">${renderedEquation}</div>`;
    };

    const handleInsertEquation = () => {
        const latex = selectedEquation || customLatex;
        if (latex.trim()) {
            const equationHTML = generateEquationHTML(latex);
            onInsertEquation(equationHTML);
            onClose();
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-6xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('mathEquation')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Categories */}
                    <div className="lg:col-span-1">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('categories')}</h4>
                        <div className="space-y-2 mb-6">
                            {Object.entries(equationCategories).map(([key, category]) => (
                                <button
                                    key={key}
                                    onClick={() => setSelectedCategory(key)}
                                    className={`w-full text-left p-2 rounded transition-colors ${
                                        selectedCategory === key
                                            ? 'bg-blue-500 text-white'
                                            : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'
                                    }`}
                                >
                                    {language === 'ar' ? category.nameAr : category.name}
                                </button>
                            ))}
                        </div>

                        {/* Custom LaTeX Input */}
                        <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('customEquation')}</h4>
                            <textarea
                                value={customLatex}
                                onChange={(e) => {
                                    setCustomLatex(e.target.value);
                                    setSelectedEquation('');
                                }}
                                placeholder={t('enterLatexCode')}
                                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                rows={3}
                            />
                        </div>
                    </div>

                    {/* Equations List */}
                    <div className="lg:col-span-1">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('equations')}</h4>
                        <div className="space-y-2 max-h-96 overflow-y-auto">
                            {equationCategories[selectedCategory as keyof typeof equationCategories].equations.map((equation, index) => (
                                <button
                                    key={index}
                                    onClick={() => {
                                        setSelectedEquation(equation.latex);
                                        setCustomLatex('');
                                    }}
                                    className={`w-full text-left p-3 rounded border transition-colors ${
                                        selectedEquation === equation.latex
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                                    }`}
                                >
                                    <div className="font-mono text-sm mb-1">{equation.latex}</div>
                                    <div className="text-xs text-gray-600 dark:text-gray-400">
                                        {language === 'ar' ? equation.descriptionAr : equation.description}
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Preview */}
                    <div className="lg:col-span-1">
                        <div className="flex justify-between items-center mb-4">
                            <h4 className="font-medium text-gray-900 dark:text-white">{t('preview')}</h4>
                            <button
                                onClick={handleInsertEquation}
                                disabled={!selectedEquation && !customLatex.trim()}
                                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
                            >
                                {t('insertEquation')}
                            </button>
                        </div>
                        <div className="border rounded p-4 bg-gray-50 dark:bg-gray-700 min-h-32">
                            {(selectedEquation || customLatex) ? (
                                <div 
                                    className="text-center"
                                    dangerouslySetInnerHTML={{ 
                                        __html: renderEquation(selectedEquation || customLatex) 
                                    }}
                                />
                            ) : (
                                <div className="flex items-center justify-center h-24 text-gray-500 dark:text-gray-400">
                                    <div className="text-center">
                                        <i className="fas fa-calculator text-2xl mb-2"></i>
                                        <p>{t('selectEquationToPreview')}</p>
                                    </div>
                                </div>
                            )}
                        </div>
                        
                        {/* LaTeX Code Display */}
                        {(selectedEquation || customLatex) && (
                            <div className="mt-4">
                                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">LaTeX Code:</h5>
                                <code className="block p-2 bg-gray-100 dark:bg-gray-600 rounded text-sm font-mono">
                                    {selectedEquation || customLatex}
                                </code>
                            </div>
                        )}
                    </div>
                </div>

                {/* CSS for equation styling */}
                <style jsx>{`
                    .fraction {
                        display: inline-block;
                        vertical-align: middle;
                        text-align: center;
                    }
                    .numerator {
                        display: block;
                        border-bottom: 1px solid currentColor;
                        padding-bottom: 2px;
                    }
                    .denominator {
                        display: block;
                        padding-top: 2px;
                    }
                `}</style>
            </div>
        </div>
    );
};

export default MathEquationEditor;
