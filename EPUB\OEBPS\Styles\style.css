
body {
    font-family: "Georgia", serif;
    line-height: 1.6;
    margin: 0;
    padding: 1em;
    background-color: #f9f9f9;
    color: #333;
}

h1, h2, h3, h4, h5, h6 {
    font-family: "Arial", sans-serif;
    font-weight: bold;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    line-height: 1.2;
    color: #1a1a1a;
    page-break-after: avoid;
}

h1 {
    font-size: 2.5em;
    text-align: center;
    margin-bottom: 1em;
}

h2 {
    font-size: 2em;
}

p {
    margin-bottom: 1em;
    text-align: justify;
}

a {
    color: #0066cc;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1em auto;
}

.cover {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.title-page {
    text-align: center;
    margin-top: 20%;
}

.chapter {
    page-break-before: always;
}
