
import React, { useState, useMemo } from 'react';
import { useBooks } from '../../hooks/useBooks';
import { Book, BookCategory } from '../../types';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../i18n';

interface DiscoveryCardProps {
    book: Book;
    onRead: (id: string) => void;
}

const DiscoveryCard = ({ book, onRead }: DiscoveryCardProps) => {
    const { t } = useLanguage();
    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow p-4 flex flex-col">
            <h3 className="text-lg font-bold text-blue-600 dark:text-blue-400 truncate">{book.title}</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">{t('by')} {book.author}</p>
            <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">{t(book.category)}</p>
            <p className="text-sm mt-2 text-gray-700 dark:text-gray-300 flex-grow line-clamp-3">{book.description}</p>
            <div className="flex justify-between items-center mt-4">
                <span className="text-lg font-semibold text-green-600 dark:text-green-400">{book.price === 'free' ? t('free') : `$${book.price.toFixed(2)}`}</span>
                <button onClick={() => onRead(book.id)} className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-3 rounded-md transition-transform transform hover:scale-105">
                    {t('read')}
                </button>
            </div>
        </div>
    );
};

const Discovery = () => {
    const { books, loading } = useBooks();
    const { t } = useLanguage();
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<BookCategory | 'all'>('all');
    
    const publishedBooks = useMemo(() => books.filter(book => book.isPublished), [books]);

    const filteredBooks = useMemo(() => {
        return publishedBooks.filter(book => {
            const matchesCategory = selectedCategory === 'all' || book.category === selectedCategory;
            const matchesSearch = searchTerm === '' ||
                book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                book.keywords.some(k => k.toLowerCase().includes(searchTerm.toLowerCase()));
            
            return matchesCategory && matchesSearch;
        });
    }, [publishedBooks, searchTerm, selectedCategory]);
    
    if (loading) {
        return <div className="text-center p-10">{t('readerLoading')}</div>;
    }

    return (
        <div className="container mx-auto">
            <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">{t('discoverTitle')}</h2>
            
            <div className="flex flex-col md:flex-row gap-4 mb-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
                <div className="relative flex-grow">
                    <input
                        type="text"
                        placeholder={t('searchPlaceholder')}
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                        className="w-full p-3 ps-10 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                    />
                    <i className="fas fa-search absolute start-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <select
                    value={selectedCategory}
                    onChange={e => setSelectedCategory(e.target.value as BookCategory | 'all')}
                    className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                >
                    <option value="all">{t('allCategories')}</option>
                    {Object.values(BookCategory).map(cat => <option key={cat} value={cat}>{t(cat)}</option>)}
                </select>
            </div>

            {filteredBooks.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredBooks.map(book => (
                        <DiscoveryCard key={book.id} book={book} onRead={(id) => navigate(`/read/${id}`)} />
                    ))}
                </div>
            ) : (
                <div className="text-center py-16 bg-white dark:bg-gray-800 rounded-lg shadow-md">
                    <i className="fas fa-search-minus text-6xl text-gray-400 dark:text-gray-500"></i>
                    <h3 className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">{t('noBooksFoundTitle')}</h3>
                    <p className="mt-2 text-gray-500 dark:text-gray-400">{t('noBooksFoundSubtitle')}</p>
                </div>
            )}
        </div>
    );
};

export default Discovery;