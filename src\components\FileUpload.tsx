import React, { useState, useRef } from 'react';
import { useLanguage } from '../i18n';
import { Book, BookCategory } from '../types';

interface FileUploadProps {
    onFileImport: (book: Partial<Book>) => void;
    onClose: () => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileImport, onClose }) => {
    const { t } = useLanguage();
    const [isDragging, setIsDragging] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files && files.length > 0) {
            handleFileUpload(files[0]);
        }
    };

    const handleFileUpload = async (file: File) => {
        setIsProcessing(true);
        setError(null);

        try {
            const fileExtension = file.name.split('.').pop()?.toLowerCase();
            let content = '';
            let title = file.name.replace(/\.[^/.]+$/, '');

            switch (fileExtension) {
                case 'txt':
                    content = await readTextFile(file);
                    break;
                case 'html':
                case 'htm':
                    content = await readTextFile(file);
                    break;
                case 'pdf':
                    content = await extractTextFromPDF(file);
                    break;
                case 'docx':
                    content = await extractTextFromDocx(file);
                    break;
                default:
                    throw new Error('Unsupported file format');
            }

            const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

            const bookData: Partial<Book> = {
                title,
                content,
                wordCount,
                category: BookCategory.GENERAL,
                author: 'Imported',
                description: `Imported from ${file.name}`,
                isPublished: false,
                createdAt: new Date(),
                updatedAt: new Date()
            };

            onFileImport(bookData);
            onClose();
        } catch (err) {
            setError(err instanceof Error ? err.message : t('uploadError'));
        } finally {
            setIsProcessing(false);
        }
    };

    const readTextFile = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target?.result as string);
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    };

    const extractTextFromPDF = async (file: File): Promise<string> => {
        // For PDF extraction, we'll use a simple approach
        // In a real application, you'd use a library like pdf-parse or PDF.js
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = () => {
                // This is a simplified approach - in reality you'd need a PDF parsing library
                resolve(`PDF content from ${file.name} - Please use a proper PDF parser for production`);
            };
            reader.readAsArrayBuffer(file);
        });
    };

    const extractTextFromDocx = async (file: File): Promise<string> => {
        // For DOCX extraction, you'd typically use a library like mammoth.js
        // This is a placeholder implementation
        return new Promise((resolve) => {
            resolve(`DOCX content from ${file.name} - Please use mammoth.js for production`);
        });
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">{t('importFromFile')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                        disabled={isProcessing}
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                        isDragging
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-300 dark:border-gray-600'
                    } ${isProcessing ? 'opacity-50 pointer-events-none' : ''}`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                >
                    <i className="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p className="text-gray-600 dark:text-gray-400 mb-2">
                        {isProcessing ? t('fileUploaded') : t('dragDropFiles')}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">
                        {t('supportedFormats')}
                    </p>
                    <button
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isProcessing}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors disabled:opacity-50"
                    >
                        {isProcessing ? t('fileUploaded') : t('selectFiles')}
                    </button>
                    <input
                        ref={fileInputRef}
                        type="file"
                        accept=".txt,.html,.htm,.pdf,.docx"
                        onChange={handleFileSelect}
                        className="hidden"
                        disabled={isProcessing}
                    />
                </div>

                {error && (
                    <div className="mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md">
                        <p className="text-red-700 dark:text-red-400 text-sm">{error}</p>
                    </div>
                )}

                {isProcessing && (
                    <div className="mt-4 flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                            {t('fileConverted')}
                        </span>
                    </div>
                )}
            </div>
        </div>
    );
};

export default FileUpload;
