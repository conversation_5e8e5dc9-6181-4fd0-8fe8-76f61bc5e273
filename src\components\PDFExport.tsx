import React, { useState } from 'react';
import { useLanguage } from '../i18n';
import { Book } from '../types';

interface PDFExportProps {
    book: Book;
    onClose: () => void;
}

const PDFExport: React.FC<PDFExportProps> = ({ book, onClose }) => {
    const { t, dir } = useLanguage();
    const [isGenerating, setIsGenerating] = useState(false);
    const [isPrinting, setIsPrinting] = useState(false);

    const generatePDF = async () => {
        setIsGenerating(true);
        try {
            // Create a new window for PDF generation
            const printWindow = window.open('', '_blank');
            if (!printWindow) {
                throw new Error('Popup blocked');
            }

            const htmlContent = `
                <!DOCTYPE html>
                <html dir="${dir}" lang="${dir === 'rtl' ? 'ar' : 'en'}">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>${book.title}</title>
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
                        
                        * {
                            margin: 0;
                            padding: 0;
                            box-sizing: border-box;
                        }
                        
                        body {
                            font-family: ${dir === 'rtl' ? 'Cairo' : 'Inter'}, sans-serif;
                            line-height: 1.6;
                            color: #1f2937;
                            background: white;
                            padding: 2cm;
                            direction: ${dir};
                        }
                        
                        .header {
                            text-align: center;
                            margin-bottom: 3rem;
                            border-bottom: 2px solid #e5e7eb;
                            padding-bottom: 2rem;
                        }
                        
                        .title {
                            font-size: 2.5rem;
                            font-weight: 700;
                            color: #1e40af;
                            margin-bottom: 1rem;
                        }
                        
                        .author {
                            font-size: 1.25rem;
                            color: #6b7280;
                            margin-bottom: 0.5rem;
                        }
                        
                        .meta {
                            font-size: 0.875rem;
                            color: #9ca3af;
                            display: flex;
                            justify-content: center;
                            gap: 2rem;
                            flex-wrap: wrap;
                        }
                        
                        .content {
                            font-size: 1rem;
                            line-height: 1.8;
                            text-align: justify;
                        }
                        
                        .content h1, .content h2, .content h3 {
                            color: #1e40af;
                            margin: 2rem 0 1rem 0;
                            page-break-after: avoid;
                        }
                        
                        .content h1 { font-size: 1.875rem; }
                        .content h2 { font-size: 1.5rem; }
                        .content h3 { font-size: 1.25rem; }
                        
                        .content p {
                            margin-bottom: 1rem;
                            orphans: 3;
                            widows: 3;
                        }
                        
                        .content img {
                            max-width: 100%;
                            height: auto;
                            margin: 1rem 0;
                            page-break-inside: avoid;
                        }
                        
                        .content table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 1rem 0;
                            page-break-inside: avoid;
                        }
                        
                        .content table th,
                        .content table td {
                            border: 1px solid #d1d5db;
                            padding: 0.5rem;
                            text-align: ${dir === 'rtl' ? 'right' : 'left'};
                        }
                        
                        .content table th {
                            background-color: #f3f4f6;
                            font-weight: 600;
                        }
                        
                        .footer {
                            margin-top: 3rem;
                            padding-top: 2rem;
                            border-top: 1px solid #e5e7eb;
                            text-align: center;
                            font-size: 0.875rem;
                            color: #6b7280;
                        }
                        
                        @media print {
                            body {
                                padding: 1cm;
                            }
                            
                            .no-print {
                                display: none !important;
                            }
                            
                            .page-break {
                                page-break-before: always;
                            }
                            
                            .content h1, .content h2, .content h3 {
                                page-break-after: avoid;
                            }
                            
                            .content p {
                                orphans: 3;
                                widows: 3;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1 class="title">${book.title}</h1>
                        <p class="author">${t('by')} ${book.author}</p>
                        <div class="meta">
                            <span>${t('category')}: ${t(book.category)}</span>
                            <span>${t('words')}: ${book.wordCount.toLocaleString()}</span>
                            <span>${t('date')}: ${new Date(book.createdAt).toLocaleDateString()}</span>
                        </div>
                    </div>
                    
                    <div class="content">
                        ${book.content}
                    </div>
                    
                    <div class="footer">
                        <p>${t('authorInfo')} - ${t('authorAffiliation')}</p>
                        <p>${t('copyright')}</p>
                        <p>${t('contactEmail')} | ${t('phoneNumbers')}</p>
                    </div>
                </body>
                </html>
            `;

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // Wait for content to load
            printWindow.onload = () => {
                setTimeout(() => {
                    setIsGenerating(false);
                }, 1000);
            };

        } catch (error) {
            console.error('Error generating PDF:', error);
            setIsGenerating(false);
        }
    };

    const handlePrint = async () => {
        setIsPrinting(true);
        try {
            await generatePDF();
            // The print dialog will be handled by the browser
            setTimeout(() => {
                setIsPrinting(false);
            }, 2000);
        } catch (error) {
            console.error('Error printing:', error);
            setIsPrinting(false);
        }
    };

    const handleDownloadPDF = async () => {
        await generatePDF();
        // Note: For actual PDF generation, you'd use a library like jsPDF or Puppeteer
        // This opens a print-friendly version that users can save as PDF
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('convertToPdf')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                        disabled={isGenerating || isPrinting}
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="space-y-4">
                    <div className="text-center">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">{book.title}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            {t('by')} {book.author} • {book.wordCount.toLocaleString()} {t('words')}
                        </p>
                    </div>

                    <div className="flex flex-col gap-3">
                        <button
                            onClick={handleDownloadPDF}
                            disabled={isGenerating || isPrinting}
                            className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-md transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
                        >
                            {isGenerating ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    {t('generatingPdf')}
                                </>
                            ) : (
                                <>
                                    <i className="fas fa-file-pdf"></i>
                                    {t('downloadPdf')}
                                </>
                            )}
                        </button>

                        <button
                            onClick={handlePrint}
                            disabled={isGenerating || isPrinting}
                            className="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-md transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
                        >
                            {isPrinting ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    {t('printing')}
                                </>
                            ) : (
                                <>
                                    <i className="fas fa-print"></i>
                                    {t('printBook')}
                                </>
                            )}
                        </button>
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400 text-center mt-4">
                        <p>{t('supportedFormats')}</p>
                        <p className="mt-1">
                            {dir === 'rtl' 
                                ? 'سيتم فتح نافذة جديدة للطباعة أو الحفظ كـ PDF'
                                : 'A new window will open for printing or saving as PDF'
                            }
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PDFExport;
