import React, { useState } from 'react';
import { useLanguage } from '../i18n';

interface FileConverterProps {
    content: string;
    title: string;
    onClose: () => void;
}

interface ConversionFormat {
    id: string;
    name: string;
    nameAr: string;
    extension: string;
    icon: string;
    description: string;
    descriptionAr: string;
    mimeType: string;
}

const FileConverter: React.FC<FileConverterProps> = ({ content, title, onClose }) => {
    const { t, language } = useLanguage();
    const [selectedFormat, setSelectedFormat] = useState<string>('');
    const [conversionSettings, setConversionSettings] = useState({
        includeStyles: true,
        includeImages: true,
        preserveFormatting: true,
        embedFonts: false,
        compressImages: false,
        quality: 'high'
    });
    const [isConverting, setIsConverting] = useState(false);
    const [conversionProgress, setConversionProgress] = useState(0);

    const conversionFormats: ConversionFormat[] = [
        {
            id: 'html',
            name: 'HTML Document',
            nameAr: 'مستند HTML',
            extension: '.html',
            icon: 'fas fa-code',
            description: 'Web-ready HTML format with embedded styles',
            descriptionAr: 'تنسيق HTML جاهز للويب مع الأنماط المدمجة',
            mimeType: 'text/html'
        },
        {
            id: 'markdown',
            name: 'Markdown',
            nameAr: 'ماركداون',
            extension: '.md',
            icon: 'fab fa-markdown',
            description: 'Lightweight markup language for documentation',
            descriptionAr: 'لغة ترميز خفيفة للتوثيق',
            mimeType: 'text/markdown'
        },
        {
            id: 'latex',
            name: 'LaTeX Document',
            nameAr: 'مستند LaTeX',
            extension: '.tex',
            icon: 'fas fa-file-code',
            description: 'Academic document format with mathematical notation',
            descriptionAr: 'تنسيق المستندات الأكاديمية مع الترميز الرياضي',
            mimeType: 'application/x-latex'
        },
        {
            id: 'rtf',
            name: 'Rich Text Format',
            nameAr: 'تنسيق النص المنسق',
            extension: '.rtf',
            icon: 'fas fa-file-alt',
            description: 'Cross-platform rich text format',
            descriptionAr: 'تنسيق النص المنسق متعدد المنصات',
            mimeType: 'application/rtf'
        },
        {
            id: 'epub',
            name: 'EPUB E-book',
            nameAr: 'كتاب إلكتروني EPUB',
            extension: '.epub',
            icon: 'fas fa-book',
            description: 'Standard e-book format for digital publishing',
            descriptionAr: 'تنسيق الكتاب الإلكتروني المعياري للنشر الرقمي',
            mimeType: 'application/epub+zip'
        },
        {
            id: 'docx',
            name: 'Microsoft Word',
            nameAr: 'مايكروسوفت وورد',
            extension: '.docx',
            icon: 'fas fa-file-word',
            description: 'Microsoft Word document format',
            descriptionAr: 'تنسيق مستند مايكروسوفت وورد',
            mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        },
        {
            id: 'txt',
            name: 'Plain Text',
            nameAr: 'نص عادي',
            extension: '.txt',
            icon: 'fas fa-file-alt',
            description: 'Simple text format without formatting',
            descriptionAr: 'تنسيق نص بسيط بدون تنسيق',
            mimeType: 'text/plain'
        },
        {
            id: 'json',
            name: 'JSON Data',
            nameAr: 'بيانات JSON',
            extension: '.json',
            icon: 'fas fa-brackets-curly',
            description: 'Structured data format for web applications',
            descriptionAr: 'تنسيق البيانات المنظمة لتطبيقات الويب',
            mimeType: 'application/json'
        }
    ];

    const convertToHTML = (content: string): string => {
        return `<!DOCTYPE html>
<html lang="${language}" dir="${language === 'ar' ? 'rtl' : 'ltr'}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: ${language === 'ar' ? 'Cairo, Arial' : 'Inter, Arial'}, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        .math-equation {
            text-align: center;
            margin: 1em 0;
            padding: 0.5em;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>${title}</h1>
    <div class="author-info">
        <p><strong>Author:</strong> Dr. Mohammed Yagoub Esmail</p>
        <p><strong>Affiliation:</strong> SUST-BME</p>
        <p><strong>Email:</strong> <EMAIL></p>
    </div>
    <hr>
    ${content}
    <hr>
    <footer>
        <p>© 2025 Dr. Mohammed Yagoub Esmail - All rights reserved</p>
    </footer>
</body>
</html>`;
    };

    const convertToMarkdown = (content: string): string => {
        let markdown = `# ${title}\n\n`;
        markdown += `**Author:** Dr. Mohammed Yagoub Esmail  \n`;
        markdown += `**Affiliation:** SUST-BME  \n`;
        markdown += `**Email:** <EMAIL>  \n\n`;
        markdown += `---\n\n`;
        
        // Convert HTML to Markdown
        let converted = content
            .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
            .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
            .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
            .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '#### $1\n\n')
            .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '##### $1\n\n')
            .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '###### $1\n\n')
            .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
            .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
            .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
            .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
            .replace(/<u[^>]*>(.*?)<\/u>/gi, '_$1_')
            .replace(/<br\s*\/?>/gi, '\n')
            .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
            .replace(/<div[^>]*>(.*?)<\/div>/gi, '$1\n\n')
            .replace(/<ul[^>]*>(.*?)<\/ul>/gi, '$1\n')
            .replace(/<ol[^>]*>(.*?)<\/ol>/gi, '$1\n')
            .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
            .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
            .replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi, '![$2]($1)')
            .replace(/<[^>]*>/g, ''); // Remove remaining HTML tags
        
        markdown += converted;
        markdown += `\n\n---\n\n*© 2025 Dr. Mohammed Yagoub Esmail - All rights reserved*`;
        
        return markdown;
    };

    const convertToLaTeX = (content: string): string => {
        let latex = `\\documentclass[12pt,a4paper]{article}
\\usepackage[utf8]{inputenc}
\\usepackage[${language === 'ar' ? 'arabic' : 'english'}]{babel}
\\usepackage{amsmath}
\\usepackage{amsfonts}
\\usepackage{amssymb}
\\usepackage{graphicx}
\\usepackage{hyperref}
\\usepackage{geometry}
\\geometry{margin=1in}

\\title{${title}}
\\author{Dr. Mohammed Yagoub Esmail \\\\ SUST-BME \\\\ \\texttt{<EMAIL>}}
\\date{\\today}

\\begin{document}

\\maketitle

\\begin{abstract}
This document was generated from the BioMedLib platform.
\\end{abstract}

`;
        
        // Convert HTML to LaTeX
        let converted = content
            .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\\section{$1}\n')
            .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\\subsection{$1}\n')
            .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\\subsubsection{$1}\n')
            .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '\\textbf{$1}')
            .replace(/<b[^>]*>(.*?)<\/b>/gi, '\\textbf{$1}')
            .replace(/<em[^>]*>(.*?)<\/em>/gi, '\\textit{$1}')
            .replace(/<i[^>]*>(.*?)<\/i>/gi, '\\textit{$1}')
            .replace(/<u[^>]*>(.*?)<\/u>/gi, '\\underline{$1}')
            .replace(/<br\s*\/?>/gi, '\\\\\n')
            .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
            .replace(/<div[^>]*class="math-equation"[^>]*data-latex="([^"]*)"[^>]*>.*?<\/div>/gi, '\\begin{equation}\n$1\n\\end{equation}\n')
            .replace(/<[^>]*>/g, ''); // Remove remaining HTML tags
        
        latex += converted;
        latex += `\n\n\\end{document}`;
        
        return latex;
    };

    const convertToPlainText = (content: string): string => {
        let text = `${title}\n`;
        text += `${'='.repeat(title.length)}\n\n`;
        text += `Author: Dr. Mohammed Yagoub Esmail\n`;
        text += `Affiliation: SUST-BME\n`;
        text += `Email: <EMAIL>\n\n`;
        text += `${'-'.repeat(50)}\n\n`;
        
        // Convert HTML to plain text
        let converted = content
            .replace(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi, '\n$1\n' + '-'.repeat(20) + '\n')
            .replace(/<br\s*\/?>/gi, '\n')
            .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
            .replace(/<div[^>]*>(.*?)<\/div>/gi, '$1\n\n')
            .replace(/<[^>]*>/g, ''); // Remove all HTML tags
        
        text += converted;
        text += `\n\n${'-'.repeat(50)}\n`;
        text += `© 2025 Dr. Mohammed Yagoub Esmail - All rights reserved`;
        
        return text;
    };

    const convertToJSON = (content: string): string => {
        const jsonData = {
            title: title,
            author: {
                name: "Dr. Mohammed Yagoub Esmail",
                affiliation: "SUST-BME",
                email: "<EMAIL>",
                phone: ["+249912867327", "+966538076790"]
            },
            content: {
                html: content,
                plainText: content.replace(/<[^>]*>/g, ''),
                wordCount: content.replace(/<[^>]*>/g, '').split(/\s+/).length,
                language: language
            },
            metadata: {
                createdAt: new Date().toISOString(),
                platform: "BioMedLib",
                version: "1.0.0",
                copyright: "© 2025 Dr. Mohammed Yagoub Esmail"
            }
        };
        
        return JSON.stringify(jsonData, null, 2);
    };

    const performConversion = async () => {
        if (!selectedFormat) {
            alert(t('pleaseSelectFormat'));
            return;
        }

        setIsConverting(true);
        setConversionProgress(0);

        // Simulate conversion progress
        const progressInterval = setInterval(() => {
            setConversionProgress(prev => {
                if (prev >= 90) {
                    clearInterval(progressInterval);
                    return 90;
                }
                return prev + 10;
            });
        }, 200);

        setTimeout(() => {
            let convertedContent = '';
            let fileName = '';
            let mimeType = '';

            const format = conversionFormats.find(f => f.id === selectedFormat);
            if (!format) return;

            fileName = `${title.replace(/[^a-zA-Z0-9]/g, '_')}${format.extension}`;
            mimeType = format.mimeType;

            switch (selectedFormat) {
                case 'html':
                    convertedContent = convertToHTML(content);
                    break;
                case 'markdown':
                    convertedContent = convertToMarkdown(content);
                    break;
                case 'latex':
                    convertedContent = convertToLaTeX(content);
                    break;
                case 'txt':
                    convertedContent = convertToPlainText(content);
                    break;
                case 'json':
                    convertedContent = convertToJSON(content);
                    break;
                case 'rtf':
                    convertedContent = `{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}} \\f0\\fs24 ${content.replace(/<[^>]*>/g, '')}}`;
                    break;
                default:
                    convertedContent = content;
            }

            // Create and download file
            const blob = new Blob([convertedContent], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            setConversionProgress(100);
            setTimeout(() => {
                setIsConverting(false);
                setConversionProgress(0);
            }, 1000);
        }, 2000);
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('fileConverter')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Format Selection */}
                    <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-4">{t('selectFormat')}</h4>
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-6">
                            {conversionFormats.map(format => (
                                <div
                                    key={format.id}
                                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                                        selectedFormat === format.id
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                                    }`}
                                    onClick={() => setSelectedFormat(format.id)}
                                >
                                    <div className="flex items-center mb-2">
                                        <i className={`${format.icon} text-lg mr-3`}></i>
                                        <span className="font-medium">
                                            {language === 'ar' ? format.nameAr : format.name}
                                        </span>
                                    </div>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {language === 'ar' ? format.descriptionAr : format.description}
                                    </p>
                                    <span className="text-xs text-blue-600 dark:text-blue-400 font-mono">
                                        {format.extension}
                                    </span>
                                </div>
                            ))}
                        </div>

                        {/* Conversion Settings */}
                        <div>
                            <h5 className="font-medium text-gray-900 dark:text-white mb-3">{t('conversionSettings')}</h5>
                            <div className="space-y-2">
                                {[
                                    { key: 'includeStyles', label: t('includeStyles') },
                                    { key: 'includeImages', label: t('includeImages') },
                                    { key: 'preserveFormatting', label: t('preserveFormatting') },
                                    { key: 'embedFonts', label: t('embedFonts') },
                                    { key: 'compressImages', label: t('compressImages') }
                                ].map(option => (
                                    <label key={option.key} className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={conversionSettings[option.key as keyof typeof conversionSettings] as boolean}
                                            onChange={(e) => setConversionSettings(prev => ({ 
                                                ...prev, 
                                                [option.key]: e.target.checked 
                                            }))}
                                            className="mr-2"
                                        />
                                        <span className="text-sm text-gray-700 dark:text-gray-300">
                                            {option.label}
                                        </span>
                                    </label>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Preview and Actions */}
                    <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-4">{t('preview')}</h4>
                        
                        <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-700 mb-4 h-64 overflow-y-auto">
                            <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                                {t('documentPreview')} - {title}
                            </div>
                            <div 
                                className="prose dark:prose-invert max-w-none text-xs"
                                dangerouslySetInnerHTML={{ __html: content.substring(0, 500) + '...' }}
                            />
                        </div>

                        {/* Conversion Progress */}
                        {isConverting && (
                            <div className="mb-4">
                                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                    <span>{t('converting')}</span>
                                    <span>{conversionProgress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div 
                                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${conversionProgress}%` }}
                                    ></div>
                                </div>
                            </div>
                        )}

                        <div className="space-y-3">
                            <button
                                onClick={performConversion}
                                disabled={!selectedFormat || isConverting}
                                className="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors flex items-center justify-center"
                            >
                                <i className={`fas ${isConverting ? 'fa-spinner fa-spin' : 'fa-download'} mr-2`}></i>
                                {isConverting ? t('converting') : t('convertAndDownload')}
                            </button>
                        </div>

                        {/* Selected Format Info */}
                        {selectedFormat && (
                            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                                {(() => {
                                    const format = conversionFormats.find(f => f.id === selectedFormat);
                                    return format ? (
                                        <div>
                                            <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                                                {t('selectedFormat')}
                                            </h5>
                                            <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                                <div><strong>{t('format')}:</strong> {language === 'ar' ? format.nameAr : format.name}</div>
                                                <div><strong>{t('extension')}:</strong> {format.extension}</div>
                                                <div><strong>{t('description')}:</strong> {language === 'ar' ? format.descriptionAr : format.description}</div>
                                            </div>
                                        </div>
                                    ) : null;
                                })()}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FileConverter;
