<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BioMedLib - Biomedical Engineering E-Book Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#1a56db',
                secondary: '#0f4c75',
                accent: '#5b84b1',
                card: '#f3f5f9',
                'dark-blue': '#072D44'
              }
            }
          }
        }
    </script>
    <style>
        .text-editor {
            min-height: 70vh;
            border: 1px solid #e2e8f0;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background: white;
        }
        .text-editor:focus {
            outline: 2px solid #1a56db;
            outline-offset: -2px;
        }
        .textarea-overlay {
            position: relative;
        }
        #editorContent::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            background: linear-gradient(rgba(255,255,255,0) 0%, rgba(255,255,255,0) 90%, rgba(243,245,249,0.8) 100%);
        }
        .completed-checkmark {
            background-color: #1a56db;
            color: white;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .category-card {
            transition: all 0.3s ease;
            border-left: 4px solid #0f4c75;
        }
        .category-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .category-card.biological {
            border-left-color: #10b981;
        }
        .category-card.instrumentation {
            border-left-color: #f59e0b;
        }
        .category-card.sensors {
            border-left-color: #8b5cf6;
        }
        .tag-bubble {
            background-color: #e0f2fe;
            color: #0369a1;
        }
        .book-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-bottom: 3px solid #1a56db;
        }
        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .stats-card {
            border-radius: 0.5rem;
            background: linear-gradient(135deg, #1a56db 0%, #0f4c75 100%);
            color: white;
        }
        .stat-icon {
            background-color: rgba(255,255,255,0.2);
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 24px;
        }
        [contenteditable] {
            padding: 12px;
        }
        .progress-bar {
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
        }
        #writingProgress::-webkit-progress-value {
            background-color: #1a56db;
            transition: width 0.5s ease;
        }
        .tooltip {
            position: relative;
            display: inline-block;
        }
        .tooltip .tooltip-text {
            visibility: hidden;
            width: 120px;
            background-color: black;
            color: white;
            text-align: center;
            border-radius: 6px;
            padding: 5px 0;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        .save-indicator {
            animation: pulse 2s infinite;
            min-width: 120px;
        }
        @media (max-width: 768px) {
            .stats-wrapper {
                flex-direction: column;
            }
            .text-editor-container {
                padding: 1rem;
            }
            .toolbar-grid {
                grid-template-columns: repeat(4, minmax(0, 1fr));
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-secondary text-white shadow-lg">
        <div class="container mx-auto px-4 py-3">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='48' height='48' fill='white'%3E%3Cpath d='M19 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h4l3 3 3-3h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 16h-4.83l-.59.59L12 20.17l-1.59-1.59-.59-.59H5V4h14v14zm-8-3h2v2h-2zm1-8c1.1 0 2 .9 2 2 0 1.1-.9 2-2 2-.53 0-1.04-.21-1.41-.59-.38-.37-.59-.88-.59-1.41 0-1.1.9-2 2-2'/%3E%3C/svg%3E" alt="BioMedLib Logo" class="mr-3">
                    <div>
                        <h1 class="text-2xl font-bold">BioMedLib</h1>
                        <p class="text-accent text-sm">Biomedical Engineering E-Book Platform</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="discoverBtn" class="px-4 py-2 rounded-lg hover:bg-primary transition-colors">
                        <i class="fas fa-book mr-2"></i>Discover
                    </button>
                    <button id="libraryBtn" class="px-4 py-2 rounded-lg hover:bg-primary transition-colors">
                        <i class="fas fa-book-open mr-2"></i>My Library
                    </button>
                    <button id="newBookBtn" class="px-4 py-2 bg-primary rounded-lg hover:bg-blue-800 transition-colors flex items-center">
                        <i class="fas fa-plus mr-2"></i> New Book
                    </button>
                    <div class="relative">
                        <img src="https://ui-avatars.com/api/?name=Dr+Yagoub&background=1a56db&color=fff&size=40" alt="User" class="w-10 h-10 rounded-full cursor-pointer">
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <!-- Dashboard Section -->
        <section id="dashboardSection" class="mb-12">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-3xl font-bold text-dark-blue flex items-center">
                    <i class="fas fa-home mr-3 text-primary"></i>Dashboard
                </h2>
                <div class="text-sm text-gray-600">
                    Welcome back, Dr. Mohammed Yagoub Esmail!
                </div>
            </div>

            <!-- Stats Section -->
            <div class="stats-wrapper grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
                <div class="stats-card p-6 flex flex-col">
                    <div class="flex items-center mb-4">
                        <div class="stat-icon mr-4">
                            <i class="fas fa-book"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">12</h3>
                            <p class="text-blue-100">Total Books</p>
                        </div>
                    </div>
                    <progress id="bookProgress" class="w-full h-2" value="75" max="100"></progress>
                    <div class="flex justify-between text-sm mt-1 text-blue-100">
                        <span>5 in draft</span>
                        <span>7 published</span>
                    </div>
                </div>
                
                <div class="stats-card p-6 flex flex-col">
                    <div class="flex items-center mb-4">
                        <div class="stat-icon mr-4">
                            <i class="fas fa-font"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">126,542</h3>
                            <p class="text-blue-100">Total Words Written</p>
                        </div>
                    </div>
                    <progress id="wordProgress" class="w-full h-2" value="43" max="100"></progress>
                    <div class="flex justify-between text-sm mt-1 text-blue-100">
                        <span>0 words</span>
                        <span>150k goal</span>
                    </div>
                </div>
                
                <div class="stats-card p-6 flex flex-col">
                    <div class="flex items-center mb-4">
                        <div class="stat-icon mr-4">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">8,753</h3>
                            <p class="text-blue-100">Total Readers</p>
                        </div>
                    </div>
                    <progress id="readerProgress" class="w-full h-2" value="62" max="100"></progress>
                    <div class="flex justify-between text-sm mt-1 text-blue-100">
                        <span>This month</span>
                        <span>14k goal</span>
                    </div>
                </div>
            </div>

            <!-- Categories Section -->
            <div class="mb-12">
                <h3 class="text-xl font-bold text-dark-blue mb-4">Top Biomedical Categories</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Category Cards -->
                    <div class="category-card biological bg-white rounded-lg shadow-md p-5">
                        <div class="flex items-center mb-2">
                            <div class="bg-green-100 text-green-800 p-3 rounded-full mr-3">
                                <i class="fas fa-dna"></i>
                            </div>
                            <h4 class="font-bold text-gray-800">Biomolecular Engineering</h4>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Study of biomolecules and their applications in medicine</p>
                        <span class="tag-bubble px-3 py-1 rounded-full text-xs inline-block">Books: 24</span>
                    </div>
                    
                    <div class="category-card bg-white rounded-lg shadow-md p-5">
                        <div class="flex items-center mb-2">
                            <div class="bg-blue-100 text-blue-800 p-3 rounded-full mr-3">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <h4 class="font-bold text-gray-800">Medical Instrumentation</h4>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Design and application of medical devices</p>
                        <span class="tag-bubble px-3 py-1 rounded-full text-xs inline-block">Books: 18</span>
                    </div>
                    
                    <div class="category-card sensors bg-white rounded-lg shadow-md p-5">
                        <div class="flex items-center mb-2">
                            <div class="bg-purple-100 text-purple-800 p-3 rounded-full mr-3">
                                <i class="fas fa-wave-square"></i>
                            </div>
                            <h4 class="font-bold text-gray-800">Biosensors & Transducers</h4>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Sensor technology for biological measurements</p>
                        <span class="tag-bubble px-3 py-1 rounded-full text-xs inline-block">Books: 31</span>
                    </div>
                    
                    <div class="category-card instrumentation bg-white rounded-lg shadow-md p-5">
                        <div class="flex items-center mb-2">
                            <div class="bg-yellow-100 text-yellow-800 p-3 rounded-full mr-3">
                                <i class="fas fa-x-ray"></i>
                            </div>
                            <h4 class="font-bold text-gray-800">Medical Imaging</h4>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Advanced techniques for biomedical visualization</p>
                        <span class="tag-bubble px-3 py-1 rounded-full text-xs inline-block">Books: 27</span>
                    </div>
                    
                    <div class="category-card bg-white rounded-lg shadow-md p-5">
                        <div class="flex items-center mb-2">
                            <div class="bg-red-100 text-red-800 p-3 rounded-full mr-3">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <h4 class="font-bold text-gray-800">Biomechanics</h4>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Mechanical principles applied to biological systems</p>
                        <span class="tag-bubble px-3 py-1 rounded-full text-xs inline-block">Books: 15</span>
                    </div>
                    
                    <div class="category-card bg-white rounded-lg shadow-md p-5">
                        <div class="flex items-center mb-2">
                            <div class="bg-indigo-100 text-indigo-800 p-3 rounded-full mr-3">
                                <i class="fas fa-procedures"></i>
                            </div>
                            <h4 class="font-bold text-gray-800">Rehabilitation Engineering</h4>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">Technologies to assist individuals with disabilities</p>
                        <span class="tag-bubble px-3 py-1 rounded-full text-xs inline-block">Books: 22</span>
                    </div>
                </div>
            </div>

            <!-- Recent Books Section -->
            <div>
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-dark-blue">Your Recent Books</h3>
                    <a href="#" class="text-primary hover:underline">View All</a>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Book Cards -->
                    <div class="book-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl">
                        <div class="p-5">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-bold text-lg">Biomedical Signal Processing</h4>
                                <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded">Published</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Advanced techniques for ECG, EEG, and EMG analysis</p>
                            <div class="flex flex-wrap gap-1 mb-4">
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Signal Processing</span>
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Electrophysiology</span>
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Filtering</span>
                            </div>
                            <div class="flex justify-between items-center text-sm text-gray-500">
                                <div>
                                    <i class="fas fa-font"></i>
                                    <span>42,750 words</span>
                                </div>
                                <div>
                                    <i class="fas fa-user"></i>
                                    <span>1,243 readers</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-5 py-3 flex justify-between items-center">
                            <button class="text-blue-500 hover:text-blue-700">
                                <i class="fas fa-edit mr-1"></i> Edit
                            </button>
                            <button class="text-green-500 hover:text-green-700">
                                <i class="fas fa-download mr-1"></i> Export
                            </button>
                        </div>
                    </div>
                    
                    <div class="book-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl">
                        <div class="p-5">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-bold text-lg">Principles of MRI Technology</h4>
                                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded">Draft</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Fundamentals of Magnetic Resonance Imaging systems</p>
                            <div class="flex flex-wrap gap-1 mb-4">
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Imaging</span>
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Radiology</span>
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Physics</span>
                            </div>
                            <div class="flex justify-between items-center text-sm text-gray-500">
                                <div>
                                    <i class="fas fa-font"></i>
                                    <span>23,415 words</span>
                                </div>
                                <div>
                                    <i class="fas fa-user"></i>
                                    <span>Not published</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-5 py-3 flex justify-between items-center">
                            <button class="text-blue-500 hover:text-blue-700">
                                <i class="fas fa-edit mr-1"></i> Edit
                            </button>
                            <button class="text-purple-500 hover:text-purple-700">
                                <i class="fas fa-share-alt mr-1"></i> Publish
                            </button>
                        </div>
                    </div>
                    
                    <div class="book-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl">
                        <div class="p-5">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-bold text-lg">Biosensors in Diagnostics</h4>
                                <span class="bg-green-100 text-green-800 text-xs px  py-1 rounded">Featured</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">Current and emerging sensor technologies for medical diagnostics</p>
                            <div class="flex flex-wrap gap-1 mb-4">
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Diagnostics</span>
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Sensors</span>
                                <span class="tag-bubble px-2 py-1 text-xs rounded">Nanotechnology</span>
                            </div>
                            <div class="flex justify-between items-center text-sm text-gray-500">
                                <div>
                                    <i class="fas fa-font"></i>
                                    <span>37,892 words</span>
                                </div>
                                <div>
                                    <i class="fas fa-user"></i>
                                    <span>5,629 readers</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-5 py-3 flex justify-between items-center">
                            <button class="text-blue-500 hover:text-blue-700">
                                <i class="fas fa-edit mr-1"></i> Edit
                            </button>
                            <button class="text-yellow-500 hover:text-yellow-700">
                                <i class="fas fa-chart-line mr-1"></i> Stats
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Editor Section (Initially Hidden) -->
        <section id="editorSection" class="hidden mb-12">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-dark-blue flex items-center">
                    <i class="fas fa-feather-alt mr-3 text-primary"></i>Book Editor
                </h2>
                <button id="saveBtn" class="save-indicator px-4 py-2 bg-green-500 rounded-lg text-white">
                    <i class="fas fa-check-circle mr-2"></i>Saved!
                </button>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-6">
                <div class="p-5 border-b">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="bookTitle">Book Title</label>
                            <input type="text" id="bookTitle" placeholder="Enter book title" class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="bookCategory">Category</label>
                            <select id="bookCategory" class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                                <option>Medical Instrumentation & Devices</option>
                                <option>Sensors & Transducers</option>
                                <option>Biological Sciences</option>
                                <option>Electronics in Biomedicine</option>
                                <option>Fundamental Engineering Sciences</option>
                                <option>Tissue Engineering</option>
                                <option>Rehabilitation Engineering</option>
                                <option>Clinical Engineering</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="bookKeywords">Keywords</label>
                        <input type="text" id="bookKeywords" placeholder="Add comma-separated keywords" class="shadow appearance-none border rounded w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <div class="flex flex-wrap gap-2 mt-2">
                            <span class="tag-bubble px-3 py-1 rounded-full">Biomedical Sensors <button class="ml-2 text-gray-500 hover:text-gray-700">×</button></span>
                            <span class="tag-bubble px-3 py-1 rounded-full">Health Monitoring <button class="ml-2 text-gray-500 hover:text-gray-700">×</button></span>
                        </div>
                    </div>
                </div>
                
                <div class="text-editor-container p-5">
                    <div class="mb-4">
                        <h3 class="text-lg font-bold mb-2">Auto-save every 5 seconds <span class="text-xs bg-blue-500 text-white px-2 py-1 rounded">Active</span></h3>
                        
                        <div class="toolbar-grid grid grid-cols-2 sm:grid-colsDate Created: 4 lg:grid-cols-8 gap-1 mb-4">
                            <!-- Formatting Buttons -->
                            <div class="tooltip">
                                <button data-command="bold" class="w-full py-2 bg-gray-100 hover:bg-gray-200 rounded">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <span class="tooltip-text">Bold (Ctrl+B)</span>
                            </div>
                            <div class="tooltip">
                                <button data-command="italic" class="w-full py-2 bg-gray-100 hover:bg-gray-200 rounded">
                                    <i class="fas fa-italic"></i>
                                </button>
                                <span class="tooltip-text">Italic (Ctrl+I)</span>
                            </div>
                            <div class="tooltip">
                                <button data-command="underline" class="w-full py-2 bg-gray-100 hover:bg-gray-200 rounded">
                                    <i class="fas fa-underline"></i>
                                </button>
                                <span class="tooltip-text">Underline (Ctrl+U)</span>
                            </div>
                            <div class="col-span-2 lg:col-span-1">
                                <button data-command="insertUnorderedList" class="w-full py-2 bg-gray-100 hover:bg-gray-200 rounded">
                                    <i class="fas fa-list-ul"></i>
                                </button>
                            </div>
                            <div class="col-span-2 lg:col-span-1">
                                <button data-command="insertOrderedList" class="w-full py-2 bg-gray-100 hover:bg-gray-200 rounded">
                                    <i class="fas fa-list-ol"></i>
                                </button>
                            </div>
                            <div class="col-span-2">
                                <button data-command="formatBlock" data-value="blockquote" class="w-full py-2 bg-gray-100 hover:bg-gray-200 rounded">
                                    <i class="fas fa-quote-right mr-1"></i> Quote
                                </button>
                            </div>
                            <div class="col-span-2">
                                <button data-command="insertHorizontalRule" class="w-full py-2 bg-gray-100 hover:bg-gray-200 rounded">
                                    <i class="fas fa-grip-lines mr-1"></i> Line Break
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex justify-between mb-2 text-sm">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-font mr-1"></i>
                                <span>Word Count: <span id="wordCount">0</span></span>
                            </div>
                            <div class="flex">
                                <button class="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded mr-2">
                                    <i class="fas fa-book-medical mr-1"></i> Insert Image
                                </button>
                                <button class="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded">
                                    <i class="fas fa-table mr-1"></i> Insert Table
                                </button>
                            </div>
                        </div>
                        
                        <div class="textarea-overlay">
                            <div id="editorContent" class="text-editor w-full p-4 mb-3" contenteditable="true">Type your content here...</div>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center mt-6">
                        <div class="flex space-x-3">
                            <button class="px-6 py-3 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors">
                                <i class="fas fa-file-download mr-2"></i>Export Draft
                            </button>
                            <button class="px-6 py-3 bg-primary hover:bg-blue-800 text-white rounded-lg transition-colors">
                                <i class="fas fa-file-export mr-2"></i>Publish Book
                            </button>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">Last saved: <span id="lastSaved">Just now</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-secondary text-white">
        <div class="container mx-auto px-4 py-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center mb-3">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='36' height='36' fill='white'%3E%3Cpath d='M19 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h4l3 3 3-3h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 16h-4.83l-.59.59L12 20.17l-1.59-1.59-.59-.59H5V4h14v14zm-8-3h2v2h-2zm1-8c1.1 0 2 .9 2 2 0 1.1-.9 2-2 2-.53 0-1.04-.21-1.41-.59-.38-.37-.59-.88-.59-1.41 0-1.1.9-2 2-2'/%3E%3C/svg%3E" alt="BioMedLib Logo" class="mr-3">
                        <p class="text-lg">BioMedLib</p>
                    </div>
                    <p class="text-sm text-blue-200">Advanced E-Book Platform for Biomedical Engineering</p>
                </div>
                <div class="text-center md:text-right">
                    <h4 class="font-bold mb-3">Author</h4>
                    <p>Dr. Mohammed Yagoub Esmail</p>
                    <p class="mt-1 text-sm text-blue-200">SUST - Biomedical Engineering Department</p>
                    <p class="text-sm mt-1">
                        <i class="fas fa-envelope mr-1"></i><EMAIL>
                    </p>
                    <p class="text-sm mt-1">
                        <i class="fas fa-phone mr-1"></i>+249912867327, +966538076790
                    </p>
                    <p class="text-xs mt-3">© 2025 All Rights Reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // DOM Elements
        const editorSection = document.getElementById('editorSection');
        const editorContent = document.getElementById('editorContent');
        const wordCount = document.getElementById('wordCount');
        const lastSaved = document.getElementById('lastSaved');
        const saveBtn = document.getElementById('saveBtn');
        const saveIndicator = document.querySelector('.save-indicator');
        const newBookBtn = document.getElementById('newBookBtn');
        const libraryBtn = document.getElementById('libraryBtn');
        const discoverBtn = document.getElementById('discoverBtn');
        
        // Editor Variables
        let saveTimer = null;
        let wordCountInterval = null;
        
        // Keyboard Shortcuts
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 's') {
                e.preventDefault();
                saveContent();
            }
            
            if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'b') {
                e.preventDefault();
                document.execCommand('bold', false, null);
            }
            
            if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'i') {
                e.preventDefault();
                document.execCommand('italic', false, null);
            }
            
            if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'u') {
                e.preventDefault();
                document.execCommand('underline', false, null);
            }
        });
        
        // Editor Functions
        document.querySelectorAll('[data-command]').forEach(button => {
            button.addEventListener('click', () => {
                const command = button.getAttribute('data-command');
                const value = button.getAttribute('data-value') || null;
                
                // Focus the editor
                editorContent.focus();
                
                // Execute command
                document.execCommand(command, false, value);
                
                // Update word count
                updateWordCount();
            });
        });
        
        function updateWordCount() {
            const text = editorContent.innerText.trim();
            const words = text ? text.split(/\s+/).length : 0;
            wordCount.textContent = words;
        }
        
        function saveContent() {
            // Save indication animation
            saveIndicator.classList.add('bg-green-600');
            
            setTimeout(() => {
                saveIndicator.classList.remove('bg-green-600');
            }, 800);
            
            // Update last saved time
            const now = new Date();
            lastSaved.textContent = `${now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
            
            // Save to localStorage
            localStorage.setItem('bookDraft', editorContent.innerHTML);
            localStorage.setItem('bookTitle', document.getElementById('bookTitle').value);
            localStorage.setItem('bookCategory', document.getElementById('bookCategory').value);
            
            console.log('Content saved');
        }
        
        function startAutoSave() {
            // Setup auto-save
            if (saveTimer) clearInterval(saveTimer);
            saveTimer = setInterval(saveContent, 5000);
            
            // Setup word count updater
            if (wordCountInterval) clearInterval(wordCountInterval);
            wordCountInterval = setInterval(updateWordCount, 1000);
        }
        
        // Event Listeners
        newBookBtn.addEventListener('click', () => {
            // Show editor, hide dashboard
            document.getElementById('dashboardSection').classList.add('hidden');
            editorSection.classList.remove('hidden');
            
            // Initialize editor content if available
            const savedContent = localStorage.getItem('bookDraft');
            if (savedContent) {
                editorContent.innerHTML = savedContent;
                document.getElementById('bookTitle').value = localStorage.getItem('bookTitle') || '';
                document.getElementById('bookCategory').value = localStorage.getItem('bookCategory') || '';
            } else {
                editorContent.innerHTML = "<h2>Introduction</h2><p>Start writing your biomedical engineering book...</p>";
            }
            
            // Focus editor
            setTimeout(() => {
                editorContent.focus();
                
                // Place cursor at start
                const range = document.createRange();
                range.selectNodeContents(editorContent);
                range.collapse(true);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
            }, 100);
            
            // Start auto-saving and word counting
            startAutoSave();
        });
        
        libraryBtn.addEventListener('click', () => {
            // Implement library view functionality
            alert('Library view would display all authored books here');
        });
        
        discoverBtn.addEventListener('click', () => {
            // Implement book discovery functionality
            alert('Discovery view would show published biomedical books from the community');
        });
        
        // Initialization
        document.addEventListener('DOMContentLoaded', () => {
            updateWordCount();
            console.log('Platform initialized');
            
            // Animate progress bars
            animateProgress();
            
            // Prompt about autosave
            setTimeout(() => {
                console.log('Auto-save enabled');
            }, 2000);
        });
        
        function animateProgress() {
            const bookProgress = document.getElementById('bookProgress');
            const wordProgress = document.getElementById('wordProgress');
            const readerProgress = document.getElementById('readerProgress');
            
            setTimeout(() => {
                bookProgress.value = 75;
            }, 300);
            
            setTimeout(() => {
                wordProgress.value = 43;
            }, 600);
            
            setTimeout(() => {
                readerProgress.value = 62;
            }, 900);
        }
    </script>
</body>
</html>