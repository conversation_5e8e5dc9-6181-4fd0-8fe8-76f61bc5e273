
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useBooks } from '../../hooks/useBooks';
import { Book } from '../../types';
import { useLanguage } from '../../i18n';
import PDFExport from '../PDFExport';

const Reader = () => {
    const { bookId } = useParams<{ bookId: string }>();
    const navigate = useNavigate();
    const { getBookById } = useBooks();
    const { t, dir } = useLanguage();
    const [book, setBook] = useState<Book | null>(null);
    const [fontSize, setFontSize] = useState(16);
    const [lineHeight, setLineHeight] = useState(1.6);
    const [fontFamily, setFontFamily] = useState('Inter');
    const [readerTheme, setReaderTheme] = useState<'light' | 'dark' | 'sepia'>('light');
    const [showSettings, setShowSettings] = useState(false);
    const [readingProgress, setReadingProgress] = useState(0);
    const [showPDFExport, setShowPDFExport] = useState(false);

    useEffect(() => {
        if (bookId) {
            const foundBook = getBookById(bookId);
            if (foundBook) {
                setBook(foundBook);
            } else {
                // if book not found (e.g. not published), redirect
                navigate('/discovery');
            }
        }
    }, [bookId, getBookById, navigate]);

    if (!book) {
        return <div className="text-center p-10">{t('readerLoading')}</div>;
    }

    // Reading progress tracking
    useEffect(() => {
        const handleScroll = () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const progress = (scrollTop / docHeight) * 100;
            setReadingProgress(Math.min(100, Math.max(0, progress)));
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const handleDownload = () => {
        const element = document.createElement("a");
        const file = new Blob([`
            <!DOCTYPE html>
            <html dir="${dir}">
                <head>
                  <meta charset="UTF-8">
                  <title>${book.title}</title>
                  <style>
                    body {
                        font-family: ${fontFamily}, sans-serif;
                        font-size: ${fontSize}px;
                        line-height: ${lineHeight};
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 2rem;
                        background-color: ${readerTheme === 'dark' ? '#1f2937' : readerTheme === 'sepia' ? '#f7f3e9' : '#ffffff'};
                        color: ${readerTheme === 'dark' ? '#f9fafb' : '#1f2937'};
                    }
                    h1, h2, h3 { color: ${readerTheme === 'dark' ? '#60a5fa' : '#2563eb'}; }
                  </style>
                </head>
                <body>
                    <h1>${book.title}</h1>
                    <h2>${t('by')} ${book.author}</h2>
                    <p><strong>${t('category')}:</strong> ${t(book.category)}</p>
                    <p><strong>${t('words')}:</strong> ${book.wordCount.toLocaleString()}</p>
                    <hr/>
                    ${book.content}
                </body>
            </html>
        `], {type: 'text/html;charset=UTF-8'});
        element.href = URL.createObjectURL(file);
        element.download = `${book.title.replace(/\s/g, '_')}.html`;
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }

    const getThemeClasses = () => {
        switch (readerTheme) {
            case 'dark':
                return 'bg-gray-900 text-gray-100';
            case 'sepia':
                return 'bg-yellow-50 text-yellow-900';
            default:
                return 'bg-white text-gray-900';
        }
    };

    return (
        <div className={`min-h-screen transition-colors duration-300 ${getThemeClasses()}`}>
            {/* Reading Progress Bar */}
            <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-700 z-50">
                <div
                    className="h-full bg-blue-500 transition-all duration-300"
                    style={{ width: `${readingProgress}%` }}
                />
            </div>

            {/* Settings Panel */}
            {showSettings && (
                <div className="fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold">{t('readingSettings')}</h3>
                            <button
                                onClick={() => setShowSettings(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                <i className="fas fa-times"></i>
                            </button>
                        </div>

                        <div className="space-y-4">
                            {/* Font Size */}
                            <div>
                                <label className="block text-sm font-medium mb-2">{t('fontSize')}: {fontSize}px</label>
                                <input
                                    type="range"
                                    min="12"
                                    max="24"
                                    value={fontSize}
                                    onChange={(e) => setFontSize(Number(e.target.value))}
                                    className="w-full"
                                />
                            </div>

                            {/* Line Height */}
                            <div>
                                <label className="block text-sm font-medium mb-2">{t('lineHeight')}: {lineHeight}</label>
                                <input
                                    type="range"
                                    min="1.2"
                                    max="2.0"
                                    step="0.1"
                                    value={lineHeight}
                                    onChange={(e) => setLineHeight(Number(e.target.value))}
                                    className="w-full"
                                />
                            </div>

                            {/* Font Family */}
                            <div>
                                <label className="block text-sm font-medium mb-2">{t('fontFamily')}</label>
                                <select
                                    value={fontFamily}
                                    onChange={(e) => setFontFamily(e.target.value)}
                                    className="w-full p-2 border rounded-md"
                                >
                                    <option value="Inter">Inter</option>
                                    <option value="Cairo">Cairo</option>
                                    <option value="Georgia">Georgia</option>
                                    <option value="Times New Roman">Times New Roman</option>
                                </select>
                            </div>

                            {/* Theme */}
                            <div>
                                <label className="block text-sm font-medium mb-2">{t('theme')}</label>
                                <div className="flex gap-2">
                                    <button
                                        onClick={() => setReaderTheme('light')}
                                        className={`px-3 py-2 rounded-md ${readerTheme === 'light' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
                                    >
                                        {t('lightTheme')}
                                    </button>
                                    <button
                                        onClick={() => setReaderTheme('dark')}
                                        className={`px-3 py-2 rounded-md ${readerTheme === 'dark' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
                                    >
                                        {t('darkTheme')}
                                    </button>
                                    <button
                                        onClick={() => setReaderTheme('sepia')}
                                        className={`px-3 py-2 rounded-md ${readerTheme === 'sepia' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
                                    >
                                        {t('sepiaTheme')}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Floating Action Buttons */}
            <div className="fixed right-4 top-20 flex flex-col gap-2 z-30">
                <button
                    onClick={() => setShowSettings(true)}
                    className="w-12 h-12 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors flex items-center justify-center"
                    title={t('readingSettings')}
                >
                    <i className="fas fa-cog"></i>
                </button>
                <button
                    onClick={handleDownload}
                    className="w-12 h-12 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-colors flex items-center justify-center"
                    title={t('download')}
                >
                    <i className="fas fa-download"></i>
                </button>
                <button
                    onClick={() => setShowPDFExport(true)}
                    className="w-12 h-12 bg-purple-500 text-white rounded-full shadow-lg hover:bg-purple-600 transition-colors flex items-center justify-center"
                    title={t('convertToPdf')}
                >
                    <i className="fas fa-file-pdf"></i>
                </button>
            </div>

            <div className="max-w-4xl mx-auto p-6 pt-16">
                {/* Header */}
                <div className="mb-8 pb-6 border-b border-gray-200 dark:border-gray-700">
                    <Link to="/discovery" className="text-blue-500 hover:underline mb-4 inline-flex items-center">
                        <i className={`fas ${dir === 'rtl' ? 'fa-arrow-right' : 'fa-arrow-left'} ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>
                        {t('backToDiscovery')}
                    </Link>
                    <h1 className="text-4xl font-bold mb-2">{book.title}</h1>
                    <p className="text-xl text-gray-600 dark:text-gray-400 mb-4">{t('by')} {book.author}</p>
                    <div className="flex flex-wrap items-center gap-4 text-sm">
                        <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full">
                            {t(book.category)}
                        </span>
                        <span className="text-gray-600 dark:text-gray-400">
                            {book.wordCount.toLocaleString()} {t('words')}
                        </span>
                        <span className="text-gray-600 dark:text-gray-400">
                            {Math.ceil(book.wordCount / 200)} {t('minutesRead')}
                        </span>
                    </div>
                </div>

                {/* Content */}
                <div
                    className="prose prose-lg max-w-none"
                    style={{
                        fontSize: `${fontSize}px`,
                        lineHeight: lineHeight,
                        fontFamily: fontFamily
                    }}
                    dangerouslySetInnerHTML={{ __html: book.content }}
                    dir={dir}
                />
            </div>

            {showPDFExport && book && (
                <PDFExport
                    book={book}
                    onClose={() => setShowPDFExport(false)}
                />
            )}
        </div>
    );
};

export default Reader;