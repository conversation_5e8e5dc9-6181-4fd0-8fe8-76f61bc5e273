
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useBooks } from '../../hooks/useBooks';
import { Book } from '../../types';
import Modal from '../Modal';
import { useLanguage } from '../../i18n';
import AdvancedPDFExport from '../AdvancedPDFExport';
import AdvancedTableEditor from '../AdvancedTableEditor';
import ImageManager from '../ImageManager';
import BiomedicalTemplates from '../BiomedicalTemplates';
import MathEquationEditor from '../MathEquationEditor';
import FileUploadManager from '../FileUploadManager';
import PrinterInterface from '../PrinterInterface';
import FileConverter from '../FileConverter';

const countWords = (htmlString: string) => {
    const text = htmlString.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    if (text === '') return 0;
    return text.split(' ').length;
};

const Editor = () => {
    const { bookId } = useParams<{ bookId: string }>();
    const navigate = useNavigate();
    const { getBookById, updateBook } = useBooks();
    const { t, dir, language } = useLanguage();
    const [book, setBook] = useState<Book | null>(null);
    const [content, setContent] = useState('');
    const [wordCount, setWordCount] = useState(0);
    const [isSaving, setIsSaving] = useState(false);
    const [isPublishModalOpen, setPublishModalOpen] = useState(false);
    const [showToolbar, setShowToolbar] = useState(true);
    const [fontSize, setFontSize] = useState(16);
    const [lineHeight, setLineHeight] = useState(1.6);
    const [showPDFExport, setShowPDFExport] = useState(false);
    const [showFileUpload, setShowFileUpload] = useState(false);
    const [showAdvancedTable, setShowAdvancedTable] = useState(false);
    const [showImageManager, setShowImageManager] = useState(false);
    const [showTemplates, setShowTemplates] = useState(false);
    const [showMathEditor, setShowMathEditor] = useState(false);
    const [showPrinterInterface, setShowPrinterInterface] = useState(false);
    const [showFileConverter, setShowFileConverter] = useState(false);
    const editorRef = useRef<HTMLDivElement>(null);
    const location = useLocation();
    
    useEffect(() => {
        if (bookId) {
            const foundBook = getBookById(bookId);
            if (foundBook) {
                setBook(foundBook);
                setContent(foundBook.content);
                setWordCount(foundBook.wordCount);
            } else {
                navigate('/library');
            }
        }
    }, [bookId, getBookById, navigate]);
    
    const saveContent = useCallback(() => {
        if (book && bookId) {
            setIsSaving(true);
            const currentContent = editorRef.current?.innerHTML || '';
            const currentWordCount = countWords(currentContent);
            updateBook(bookId, { content: currentContent, wordCount: currentWordCount });
            setWordCount(currentWordCount);
            setTimeout(() => setIsSaving(false), 1000);
        }
    }, [book, bookId, updateBook]);

    const insertTable = () => {
        const tableHTML = `
            <table style="border-collapse: collapse; width: 100%; margin: 1rem 0;">
                <thead>
                    <tr>
                        <th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;">العمود 1</th>
                        <th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;">العمود 2</th>
                        <th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5;">العمود 3</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">البيانات 1</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">البيانات 2</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">البيانات 3</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">البيانات 4</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">البيانات 5</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">البيانات 6</td>
                    </tr>
                </tbody>
            </table>
        `;
        document.execCommand('insertHTML', false, tableHTML);
    };

    const insertImage = () => {
        const url = prompt(t('enterImageUrl'));
        if (url) {
            const imageHTML = `<img src="${url}" alt="${t('image')}" style="max-width: 100%; height: auto; margin: 1rem 0;" />`;
            document.execCommand('insertHTML', false, imageHTML);
        }
    };

    const handleInsertTable = (tableHTML: string) => {
        document.execCommand('insertHTML', false, tableHTML);
    };

    const handleInsertImage = (imageHTML: string) => {
        document.execCommand('insertHTML', false, imageHTML);
    };

    const handleUseTemplate = (templateContent: string) => {
        if (editorRef.current) {
            editorRef.current.innerHTML = templateContent;
            setContent(templateContent);
            setWordCount(countWords(templateContent));
        }
    };

    const handleInsertEquation = (equationHTML: string) => {
        document.execCommand('insertHTML', false, equationHTML);
    };

    const handleImportContent = (importedContent: string) => {
        if (editorRef.current) {
            editorRef.current.innerHTML = importedContent;
            setContent(importedContent);
            setWordCount(countWords(importedContent));
        }
    };

    // Auto-save
    useEffect(() => {
        const timer = setTimeout(() => {
            if (book && content !== book.content) {
                saveContent();
            }
        }, 5000); // Auto-save every 5 seconds
        return () => clearTimeout(timer);
    }, [content, book, saveContent]);

    // Keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.ctrlKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        saveContent();
                        break;
                    case 'b':
                        e.preventDefault();
                        document.execCommand('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        document.execCommand('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        document.execCommand('underline');
                        break;
                    case 'z':
                        e.preventDefault();
                        document.execCommand('undo');
                        break;
                    case 'y':
                        e.preventDefault();
                        document.execCommand('redo');
                        break;
                    case '1':
                        e.preventDefault();
                        document.execCommand('formatBlock', false, 'h1');
                        break;
                    case '2':
                        e.preventDefault();
                        document.execCommand('formatBlock', false, 'h2');
                        break;
                    case '3':
                        e.preventDefault();
                        document.execCommand('formatBlock', false, 'h3');
                        break;
                    case 'l':
                        e.preventDefault();
                        document.execCommand('insertUnorderedList');
                        break;
                    case 'k':
                        e.preventDefault();
                        document.execCommand('insertOrderedList');
                        break;
                }
            }
        };
        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [saveContent]);
    
    const handleContentChange = (e: React.FormEvent<HTMLDivElement>) => {
        const newContent = e.currentTarget.innerHTML;
        setContent(newContent);
        setWordCount(countWords(newContent));
    };

    const handleFormat = (command: string, value?: string) => {
        document.execCommand(command, false, value);
        editorRef.current?.focus();
    };

    const handlePublish = () => {
        if (book && bookId) {
            saveContent();
            updateBook(bookId, {
                isPublished: book.isPublished,
                price: book.price,
                keywords: book.keywords,
            });
            setPublishModalOpen(false);
        }
    };

    if (!book) {
        return <div className="text-center p-10">{t('editorLoading')}</div>;
    }
    
    return (
        <div className="max-w-4xl mx-auto">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-3xl font-bold text-gray-800 dark:text-white truncate">{book.title}</h2>
                <div className="flex items-center space-x-4">
                    <button onClick={saveContent} className="px-4 py-2 rounded text-white bg-blue-500 hover:bg-blue-600 flex items-center">
                        <i className={`fas fa-save ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i> {t('save')}
                    </button>
                    <button onClick={() => setPublishModalOpen(true)} className="px-4 py-2 rounded text-white bg-green-500 hover:bg-green-600 flex items-center">
                       <i className={`fas fa-upload ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i> {t('publish')}
                    </button>
                </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
                <div className="flex items-center p-2 border-b border-gray-200 dark:border-gray-700 space-x-1 flex-wrap gap-1">
                    {/* Text Formatting */}
                    <div className="flex items-center space-x-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button onClick={() => handleFormat('bold')} className="toolbar-button" title={`${t('bold')} (Ctrl+B)`}>
                            <i className="fas fa-bold"></i>
                        </button>
                        <button onClick={() => handleFormat('italic')} className="toolbar-button" title={`${t('italic')} (Ctrl+I)`}>
                            <i className="fas fa-italic"></i>
                        </button>
                        <button onClick={() => handleFormat('underline')} className="toolbar-button" title={`${t('underline')} (Ctrl+U)`}>
                            <i className="fas fa-underline"></i>
                        </button>
                        <button onClick={() => handleFormat('strikeThrough')} className="toolbar-button" title={t('strikethrough')}>
                            <i className="fas fa-strikethrough"></i>
                        </button>
                    </div>

                    {/* Headers */}
                    <div className="flex items-center space-x-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button onClick={() => handleFormat('formatBlock', 'h1')} className="toolbar-button" title={t('heading1')}>
                            <i className="fas fa-heading"></i><sub>1</sub>
                        </button>
                        <button onClick={() => handleFormat('formatBlock', 'h2')} className="toolbar-button" title={t('heading2')}>
                            <i className="fas fa-heading"></i><sub>2</sub>
                        </button>
                        <button onClick={() => handleFormat('formatBlock', 'h3')} className="toolbar-button" title={t('heading3')}>
                            <i className="fas fa-heading"></i><sub>3</sub>
                        </button>
                    </div>

                    {/* Lists */}
                    <div className="flex items-center space-x-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button onClick={() => handleFormat('insertUnorderedList')} className="toolbar-button" title={t('bulletList')}>
                            <i className="fas fa-list-ul"></i>
                        </button>
                        <button onClick={() => handleFormat('insertOrderedList')} className="toolbar-button" title={t('numberedList')}>
                            <i className="fas fa-list-ol"></i>
                        </button>
                    </div>

                    {/* Alignment */}
                    <div className="flex items-center space-x-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button onClick={() => handleFormat('justifyLeft')} className="toolbar-button" title={t('alignLeft')}>
                            <i className="fas fa-align-left"></i>
                        </button>
                        <button onClick={() => handleFormat('justifyCenter')} className="toolbar-button" title={t('alignCenter')}>
                            <i className="fas fa-align-center"></i>
                        </button>
                        <button onClick={() => handleFormat('justifyRight')} className="toolbar-button" title={t('alignRight')}>
                            <i className="fas fa-align-right"></i>
                        </button>
                        <button onClick={() => handleFormat('justifyFull')} className="toolbar-button" title={t('alignJustify')}>
                            <i className="fas fa-align-justify"></i>
                        </button>
                    </div>

                    {/* Special */}
                    <div className="flex items-center space-x-1">
                        <button onClick={() => handleFormat('insertHorizontalRule')} className="toolbar-button" title={t('insertHorizontalLine')}>
                            <i className="fas fa-minus"></i>
                        </button>
                        <button onClick={() => handleFormat('formatBlock', 'blockquote')} className="toolbar-button" title={t('quote')}>
                            <i className="fas fa-quote-right"></i>
                        </button>
                        <button onClick={() => handleFormat('removeFormat')} className="toolbar-button" title={t('clearFormatting')}>
                            <i className="fas fa-eraser"></i>
                        </button>
                        <button onClick={() => handleFormat('undo')} className="toolbar-button" title={`${t('undo')} (Ctrl+Z)`}>
                            <i className="fas fa-undo"></i>
                        </button>
                        <button onClick={() => handleFormat('redo')} className="toolbar-button" title={`${t('redo')} (Ctrl+Y)`}>
                            <i className="fas fa-redo"></i>
                        </button>
                    </div>

                    {/* Insert Elements */}
                    <div className="flex items-center space-x-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button onClick={() => setShowAdvancedTable(true)} className="toolbar-button" title={t('advancedTable')}>
                            <i className="fas fa-table"></i>
                        </button>
                        <button onClick={() => setShowImageManager(true)} className="toolbar-button" title={t('imageManager')}>
                            <i className="fas fa-image"></i>
                        </button>
                        <button onClick={() => handleFormat('createLink', prompt(t('enterUrl')) || '')} className="toolbar-button" title={t('insertLink')}>
                            <i className="fas fa-link"></i>
                        </button>
                        <button onClick={() => setShowFileUpload(true)} className="toolbar-button" title={t('importFromFile')}>
                            <i className="fas fa-upload"></i>
                        </button>
                        <button onClick={() => setShowTemplates(true)} className="toolbar-button" title={t('biomedicalTemplate')}>
                            <i className="fas fa-file-medical"></i>
                        </button>
                        <button onClick={() => setShowMathEditor(true)} className="toolbar-button" title={t('mathEquation')}>
                            <i className="fas fa-calculator"></i>
                        </button>
                    </div>

                    {/* Export and Print */}
                    <div className="flex items-center space-x-1 border-r border-gray-300 dark:border-gray-600 pr-2">
                        <button onClick={() => setShowPDFExport(true)} className="toolbar-button" title={t('convertToPdf')}>
                            <i className="fas fa-file-pdf"></i>
                        </button>
                        <button onClick={() => setShowPrinterInterface(true)} className="toolbar-button" title={t('printerInterface')}>
                            <i className="fas fa-print"></i>
                        </button>
                        <button onClick={() => setShowFileConverter(true)} className="toolbar-button" title={t('fileConverter')}>
                            <i className="fas fa-exchange-alt"></i>
                        </button>
                        <button onClick={() => window.print()} className="toolbar-button" title={t('printBook')}>
                            <i className="fas fa-print"></i>
                        </button>
                    </div>

                    {/* Font Size Controls */}
                    <div className="flex items-center space-x-1">
                        <button
                            onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                            className="toolbar-button"
                            title={t('decreaseFontSize')}
                        >
                            <i className="fas fa-search-minus"></i>
                        </button>
                        <span className="text-sm px-2 text-gray-600 dark:text-gray-300">{fontSize}px</span>
                        <button
                            onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                            className="toolbar-button"
                            title={t('increaseFontSize')}
                        >
                            <i className="fas fa-search-plus"></i>
                        </button>
                        <button
                            onClick={() => setShowToolbar(!showToolbar)}
                            className="toolbar-button"
                            title={showToolbar ? t('hideToolbar') : t('showToolbar')}
                        >
                            <i className={`fas fa-${showToolbar ? 'eye-slash' : 'eye'}`}></i>
                        </button>
                    </div>
                </div>
                <div
                    ref={editorRef}
                    contentEditable
                    onInput={handleContentChange}
                    className="p-6 h-[60vh] overflow-y-auto focus:outline-none dark:text-gray-200"
                    style={{
                        fontSize: `${fontSize}px`,
                        lineHeight: lineHeight,
                        display: showToolbar ? 'block' : 'block'
                    }}
                    dangerouslySetInnerHTML={{ __html: content }}
                    dir={dir}
                />
                <div className="flex justify-between items-center p-2 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400">
                    <span>{t('words')}: {wordCount}</span>
                    <span className={`transition-opacity ${isSaving ? 'opacity-100' : 'opacity-0'}`}>
                        <i className="fas fa-check-circle text-green-500 me-1"></i>{t('saved')}
                    </span>
                </div>
            </div>
             <Modal isOpen={isPublishModalOpen} onClose={() => setPublishModalOpen(false)} title={t('publishSettingsTitle')}>
                <div className="space-y-4">
                    <div>
                        <label className="block mb-1 font-semibold">{t('statusLabel')}</label>
                        <select
                            value={book.isPublished ? 'published' : 'private'}
                            onChange={(e) => setBook({ ...book, isPublished: e.target.value === 'published' })}
                            className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                        >
                            <option value="private">{t('private')}</option>
                            <option value="published">{t('public')}</option>
                        </select>
                    </div>
                     <div>
                        <label className="block mb-1 font-semibold">{t('pricingLabel')}</label>
                         <div className="flex items-center space-x-2">
                            <select
                                value={book.price === 'free' ? 'free' : 'paid'}
                                onChange={(e) => setBook({ ...book, price: e.target.value === 'free' ? 'free' : (typeof book.price === 'number' ? book.price : 0) })}
                                className="w-1/3 p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="free">{t('free')}</option>
                                <option value="paid">{t('paid')}</option>
                            </select>
                            {book.price !== 'free' && (
                                <input
                                    type="number"
                                    min="0"
                                    value={book.price}
                                    onChange={(e) => setBook({ ...book, price: parseFloat(e.target.value) || 0 })}
                                    className="w-2/3 p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                                />
                            )}
                        </div>
                    </div>
                     <div>
                        <label className="block mb-1 font-semibold">{t('keywordsLabel')}</label>
                        <input
                            type="text"
                            value={book.keywords.join(language === 'ar' ? '، ' : ', ')}
                            onChange={(e) => setBook({ ...book, keywords: e.target.value.split(language === 'ar' ? '،' : ',').map(k => k.trim()) })}
                            className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
                        />
                    </div>
                    <div className="flex justify-end space-x-2">
                        <button onClick={() => setPublishModalOpen(false)} className="px-4 py-2 rounded text-gray-600 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500">{t('cancel')}</button>
                        <button onClick={handlePublish} className="px-4 py-2 rounded text-white bg-green-500 hover:bg-green-600">{t('saveSettings')}</button>
                    </div>
                </div>
            </Modal>

            {showPDFExport && book && (
                <AdvancedPDFExport
                    content={content}
                    title={book.title}
                    onClose={() => setShowPDFExport(false)}
                />
            )}

            {showAdvancedTable && (
                <AdvancedTableEditor
                    onInsertTable={handleInsertTable}
                    onClose={() => setShowAdvancedTable(false)}
                />
            )}

            {showImageManager && (
                <ImageManager
                    onInsertImage={handleInsertImage}
                    onClose={() => setShowImageManager(false)}
                />
            )}

            {showTemplates && (
                <BiomedicalTemplates
                    onUseTemplate={handleUseTemplate}
                    onClose={() => setShowTemplates(false)}
                />
            )}

            {showMathEditor && (
                <MathEquationEditor
                    onInsertEquation={handleInsertEquation}
                    onClose={() => setShowMathEditor(false)}
                />
            )}

            {showFileUpload && (
                <FileUploadManager
                    onImportContent={handleImportContent}
                    onClose={() => setShowFileUpload(false)}
                />
            )}

            {showPrinterInterface && book && (
                <PrinterInterface
                    content={content}
                    title={book.title}
                    onClose={() => setShowPrinterInterface(false)}
                />
            )}

            {showFileConverter && book && (
                <FileConverter
                    content={content}
                    title={book.title}
                    onClose={() => setShowFileConverter(false)}
                />
            )}
        </div>
    );
};

export default Editor;