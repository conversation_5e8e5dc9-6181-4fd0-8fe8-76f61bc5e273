@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Arabic text */
.arabic-text {
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.english-text {
  font-family: 'Inter', sans-serif;
  direction: ltr;
}

/* Rich text editor styles */
.prose {
  max-width: none;
}

.prose h1 {
  font-size: 2.25rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.prose h2 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
}

.prose h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.75;
}

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.25rem;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

/* Dark mode prose styles */
.dark .prose {
  color: #e5e7eb;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3 {
  color: #f9fafb;
}

.dark .prose blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

/* Line clamp utility */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* RTL support improvements */
[dir="rtl"] .me-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .ms-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .ps-10 {
  padding-left: 0.75rem;
  padding-right: 2.5rem;
}

[dir="rtl"] .start-3 {
  right: 0.75rem;
  left: auto;
}

/* Editor specific styles */
.editor-content {
  min-height: 400px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  outline: none;
}

.editor-content:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .editor-content {
  border-color: #4b5563;
  background-color: #1f2937;
  color: #e5e7eb;
}

.dark .editor-content:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Toolbar styles */
.toolbar-button {
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  color: #4b5563;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-button:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
  color: #1f2937;
}

.dark .toolbar-button {
  color: #9ca3af;
}

.dark .toolbar-button:hover {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.toolbar-button.active {
  background-color: #3b82f6;
  color: white;
  border-color: #2563eb;
}

.toolbar-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Book card hover effects */
.book-card {
  transition: all 0.3s ease;
}

.book-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success/Error messages */
.message-success {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.message-error {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.dark .message-success {
  background-color: #14532d;
  color: #bbf7d0;
  border-color: #166534;
}

.dark .message-error {
  background-color: #7f1d1d;
  color: #fecaca;
  border-color: #dc2626;
}

/* Utility classes */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced animations */
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced table styles for editor */
[contenteditable="true"] table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  border: 1px solid #d1d5db;
}

[contenteditable="true"] table th,
[contenteditable="true"] table td {
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  text-align: right;
  vertical-align: top;
}

[contenteditable="true"] table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

[contenteditable="true"] table tr:nth-child(even) {
  background-color: #f9fafb;
}

[contenteditable="true"] table tr:hover {
  background-color: #f3f4f6;
}

/* Dark mode table styles */
.dark [contenteditable="true"] table {
  border-color: #4b5563;
}

.dark [contenteditable="true"] table th,
.dark [contenteditable="true"] table td {
  border-color: #4b5563;
  color: #e5e7eb;
}

.dark [contenteditable="true"] table th {
  background-color: #374151;
  color: #f9fafb;
}

.dark [contenteditable="true"] table tr:nth-child(even) {
  background-color: #374151;
}

.dark [contenteditable="true"] table tr:hover {
  background-color: #4b5563;
}

/* Enhanced focus styles for editor */
[contenteditable="true"]:focus {
  outline: none;
  box-shadow: inset 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Image styles in editor */
[contenteditable="true"] img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

/* Link styles in editor */
[contenteditable="true"] a {
  color: #3b82f6;
  text-decoration: underline;
}

.dark [contenteditable="true"] a {
  color: #60a5fa;
}

/* Blockquote styles in editor */
[contenteditable="true"] blockquote {
  border-right: 4px solid #3b82f6;
  padding-right: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
}

.dark [contenteditable="true"] blockquote {
  border-color: #60a5fa;
  color: #9ca3af;
  background-color: #374151;
}

/* Horizontal rule styles */
[contenteditable="true"] hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, #d1d5db, transparent);
  margin: 2rem 0;
}

.dark [contenteditable="true"] hr {
  background: linear-gradient(to right, transparent, #4b5563, transparent);
}

/* Language-specific styles and transitions */
html[dir="rtl"] {
  font-family: 'Cairo', sans-serif;
}

html[dir="ltr"] {
  font-family: 'Inter', sans-serif;
}

/* Smooth language transition */
* {
  transition: font-family 0.3s ease, direction 0.3s ease;
}

/* Language switcher enhanced styles */
.language-switcher {
  position: relative;
  overflow: hidden;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.language-switcher button {
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  letter-spacing: 0.025em;
}

.language-switcher button.active {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.language-switcher button:hover:not(.active) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* RTL specific layout adjustments */
html[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

html[dir="rtl"] .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

html[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

/* Text alignment for RTL */
html[dir="rtl"] .text-left {
  text-align: right;
}

html[dir="rtl"] .text-right {
  text-align: left;
}

/* Enhanced page transitions for language switching */
.page-transition {
  transition: all 0.3s ease-in-out;
}

.page-transition.language-switching {
  opacity: 0.8;
  transform: scale(0.98);
}

/* Improved button styles for language switching */
.btn-language {
  position: relative;
  overflow: hidden;
}

.btn-language::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-language:hover::before {
  left: 100%;
}
