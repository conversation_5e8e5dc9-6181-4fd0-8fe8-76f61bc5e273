import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export enum BookCategory {
    BIOLOGICAL = 'biological',
    FUNDAMENTAL_ENGINEERING = 'fundamental_engineering',
    BIOMEDICAL_ENGINEERING = 'biomedical_engineering',
    CLINICAL_ENGINEERING = 'clinical_engineering',
    MEDICAL_DEVICES = 'medical_devices',
    BIOMATERIALS = 'biomaterials',
    BIOINFORMATICS = 'bioinformatics',
    REHABILITATION = 'rehabilitation',
    RESEARCH_METHODS = 'research_methods'
}

const translations = {
    en: {
        // App Title and Hero
        appName: 'BioMedLib',
        platformTitle: 'BME E-books',
        heroTitle: 'BioMedLib - Biomedical Engineering E-Book Platform',
        heroSubtitle: 'Create, write, edit, and publish professional biomedical engineering books with advanced features',
        getStarted: 'Get Started',
        exploreBooks: 'Explore Books',

        // Navigation
        home: 'Home',
        library: 'Library',
        discovery: 'Discovery',
        editor: 'Editor',
        reader: 'Reader',
        settings: 'Settings',

        // Language
        language: 'Language',
        english: 'English',
        arabic: 'العربية',
        
        // Common
        save: 'Save',
        saving: 'Saving...',
        cancel: 'Cancel',
        delete: 'Delete',
        edit: 'Edit',
        create: 'Create',
        read: 'Read',
        publish: 'Publish',
        unpublish: 'Unpublish',
        yes: 'Yes',
        no: 'No',
        loading: 'Loading...',
        search: 'Search',
        filter: 'Filter',
        sort: 'Sort',
        title: 'Title',
        description: 'Description',
        author: 'Author',
        category: 'Category',
        status: 'Status',
        date: 'Date',
        preview: 'Preview',
        download: 'Download',
        upload: 'Upload',
        close: 'Close',
        
        // Library
        libraryTitle: 'My Library',
        createNewBook: 'Create New Book',
        bookTitle: 'Book Title',
        bookDescription: 'Book Description',
        selectCategory: 'Select Category',
        emptyLibraryTitle: 'Your library is empty',
        emptyLibrarySubtitle: 'Start by creating your first book to build your collection',
        
        // Editor
        editorTitle: 'Editor',
        backToLibrary: 'Back to Library',
        toolbar: 'Toolbar',
        bold: 'Bold',
        italic: 'Italic',
        underline: 'Underline',
        strikethrough: 'Strikethrough',
        alignLeft: 'Align Left',
        alignCenter: 'Align Center',
        alignRight: 'Align Right',
        alignJustify: 'Justify',
        insertLink: 'Insert Link',
        insertImage: 'Insert Image',
        insertTable: 'Insert Table',
        bulletList: 'Bullet List',
        numberedList: 'Numbered List',
        fontSize: 'Font Size',
        fontFamily: 'Font Family',
        textColor: 'Text Color',
        backgroundColor: 'Background Color',
        
        // Discovery
        discoveryTitle: 'Discover Books',
        searchBooks: 'Search books...',
        allCategories: 'All Categories',
        sortBy: 'Sort by:',
        newest: 'Newest',
        oldest: 'Oldest',
        titleAZ: 'Title A-Z',
        titleZA: 'Title Z-A',
        viewMode: 'View Mode',
        gridView: 'Grid View',
        listView: 'List View',
        noBooks: 'No books found',
        noBooksSub: 'Try adjusting your search or filters',
        by: 'by',

        // Home Page
        totalBooks: 'Total Books',
        published: 'Published',
        personalLibrary: 'Personal Library',
        availableForReading: 'Available for Reading',
        richTextEditor: 'Rich Text Editor',
        richTextEditorDesc: 'Advanced editor with formatting tools and biomedical templates',
        bilingualSupport: 'Bilingual Support',
        bilingualSupportDesc: 'Full Arabic and English interface with RTL support',
        bookManagement: 'Book Management',
        bookManagementDesc: 'Organize, categorize, and manage your book collection',
        publishingSystem: 'Publishing System',
        publishingSystemDesc: 'Publish and share your books with the community',
        responsiveDesign: 'Responsive Design',
        responsiveDesignDesc: 'Works perfectly on desktop, tablet, and mobile devices',
        localStorage: 'Local Storage',
        localStorageDesc: 'Your data is stored locally for privacy and offline access',
        advancedFeaturesTitle: 'Advanced Features',
        advancedFeaturesSubtitle: 'Professional tools designed specifically for biomedical engineering content',
        recentBooksTitle: 'Recent Books',
        recentBooksSubtitle: 'Explore the latest additions to your library',
        myLibrary: 'My Library',
        readNow: 'Read Now',
        startWriting: 'Start writing your content here...',
        words: 'Words',
        advancedEditor: 'Advanced Editor',

        // Reader
        readerTitle: 'Reader',
        readingProgress: 'Reading Progress',
        backToDiscovery: 'Back to Discovery',
        
        // File Upload
        uploadFile: 'Upload File',
        dragDropFile: 'Drag and drop a file here or click to select',
        selectFile: 'Select File',
        uploading: 'Uploading',
        supportedFormats: 'Supported Formats',
        importContent: 'Import Content',
        
        // PDF Export
        advancedPDFExport: 'Advanced PDF Export',
        exportSettings: 'Export Settings',
        paperSize: 'Paper Size',
        orientation: 'Orientation',
        portrait: 'Portrait',
        landscape: 'Landscape',
        margins: 'Margins',
        includeHeader: 'Include Header',
        includeFooter: 'Include Footer',
        includePageNumbers: 'Include Page Numbers',
        includeCoverPage: 'Include Cover Page',
        
        // Printer Interface
        printerInterface: 'Printer Interface',
        availablePrinters: 'Available Printers',
        printSettings: 'Print Settings',
        copies: 'Copies',
        colorMode: 'Color Mode',
        quality: 'Quality',
        draft: 'Draft',
        normal: 'Normal',
        high: 'High',
        
        // File Converter
        fileConverter: 'File Converter',
        selectFormat: 'Select Format',
        conversionSettings: 'Conversion Settings',
        includeStyles: 'Include Styles',
        includeImages: 'Include Images',
        preserveFormatting: 'Preserve Formatting',
        embedFonts: 'Embed Fonts',
        compressImages: 'Compress Images',
        converting: 'Converting',
        convertAndDownload: 'Convert and Download',
        selectedFormat: 'Selected Format',
        format: 'Format',
        extension: 'Extension',
        pleaseSelectFormat: 'Please select a format',
        
        // Format Names
        htmlFormat: 'HTML Document',
        markdownFormat: 'Markdown',
        latexFormat: 'LaTeX Document',
        rtfFormat: 'Rich Text Format',
        epubFormat: 'EPUB E-book',
        docxFormat: 'Word Document',
        txtFormat: 'Plain Text',
        jsonFormat: 'JSON Data',
        
        // Format Descriptions
        htmlDescription: 'Web-compatible document with styling',
        markdownDescription: 'Lightweight markup for documentation',
        latexDescription: 'Academic document with mathematical notation',
        rtfDescription: 'Cross-platform rich text document',
        epubDescription: 'Standard e-book format',
        docxDescription: 'Microsoft Word compatible document',
        txtDescription: 'Simple plain text file',
        jsonDescription: 'Structured data format',
        
        // Additional Editor Features
        saved: 'Saved',
        editorLoading: 'Loading editor...',
        enterUrl: 'Enter URL:',
        enterImageUrl: 'Enter image URL:',
        image: 'Image',
        clearFormatting: 'Clear Formatting',
        decreaseFontSize: 'Decrease Font Size',
        increaseFontSize: 'Increase Font Size',
        quote: 'Quote',
        
        // Templates and Equations
        templates: 'Templates',
        equations: 'Equations',
        insertTemplate: 'Insert Template',
        insertEquation: 'Insert Equation',
        selectTemplateToPreview: 'Select a template to preview',
        selectEquationToPreview: 'Select an equation to preview',
        
        // Additional File Operations
        selectFileToPreview: 'Select a file to preview',
        
        // Print and Export Additional
        priceHighLow: 'Price: High to Low',
        priceLowHigh: 'Price: Low to High',
        printQueue: 'Print Queue',
        noPrintJobs: 'No print jobs in queue',
        color: 'Color',
        blackAndWhite: 'Black & White',
        printerStatus: 'Printer Status',
        ready: 'Ready',
        offline: 'Offline',
        busy: 'Busy',
        error: 'Error',
        paperJam: 'Paper Jam',
        outOfPaper: 'Out of Paper',
        outOfInk: 'Out of Ink',
        printerType: 'Printer Type',
        inkjet: 'Inkjet',
        laser: 'Laser',
        thermal: 'Thermal',
        colorSupport: 'Color Support',
        duplexSupport: 'Duplex Support',
        pleaseSelectPrinter: 'Please select a printer',
        printerNotReady: 'Printer is not ready',
        printJob: 'Print Job',
        queued: 'Queued',
        printing: 'Printing',
        completed: 'Completed',
        
        // Additional Status and States
        publishedStatus: 'Published',
        lastUpdated: 'Last Updated',
        noBooksMessage: 'Start by creating your first book to build your library.',
        averageWords: 'Average Words',
        totalReadership: 'Total Readership',
        ascending: 'Ascending',
        descending: 'Descending',
        books: 'Books',
        createBookTitle: 'Create New Book',
        bookTitleLabel: 'Book Title',
        authorNameLabel: 'Author Name',
        descriptionLabel: 'Description',
        keywordsLabel: 'Keywords (comma separated)',
        loadingLibrary: 'Loading library...',
        
        // Reader Additional
        lightTheme: 'Light',
        darkTheme: 'Dark',
        sepiaTheme: 'Sepia',
        minutesRead: 'minutes read',
        readerLoading: 'Loading...',
        
        // Delete Confirmation
        confirmDelete: 'Are you sure you want to delete this book?',
        deleteConfirmation: 'This action cannot be undone.',
        
        // Additional Sorting and Filtering
        allStatuses: 'All Statuses',
        wordCount: 'Word Count',
        wordCountHigh: 'Word Count (High)',
        wordCountLow: 'Word Count (Low)',
        
        // Author Info
        authorInfo: 'Dr. Mohammed Yagoub Esmail',
        authorAffiliation: 'SUST - BME',
        copyright: '© 2025 All Rights Reserved',
        contactEmail: '<EMAIL>',
        phoneNumbers: 'Phone: +249912867327, +966538076790',
        
        // Categories
        [BookCategory.BIOLOGICAL]: 'Biological Sciences',
        [BookCategory.FUNDAMENTAL_ENGINEERING]: 'Fundamental Engineering',
        [BookCategory.BIOMEDICAL_ENGINEERING]: 'Biomedical Engineering',
        [BookCategory.CLINICAL_ENGINEERING]: 'Clinical Engineering',
        [BookCategory.MEDICAL_DEVICES]: 'Medical Devices',
        [BookCategory.BIOMATERIALS]: 'Biomaterials',
        [BookCategory.BIOINFORMATICS]: 'Bioinformatics',
        [BookCategory.REHABILITATION]: 'Rehabilitation Engineering',
        [BookCategory.RESEARCH_METHODS]: 'Research Methods',

        // Project Orion
        projectOrionTitle: 'Project Orion - Integrated Book Authoring & Publishing Web Application',
        projectOrionSubtitle: 'A comprehensive platform designed specifically for biomedical engineering content creation',
        backToHome: 'Back to Home',

        // Navigation Sections
        visionAndGoal: 'Vision & Goal',
        targetAudience: 'Target Audience',
        coreFeatures: 'Core Features',
        userExperience: 'User Experience',
        technicalConsiderations: 'Technical Considerations',
        businessModel: 'Business Model',

        // Vision Section
        visionDescription: 'The project aims to develop a comprehensive and integrated web application for book authoring, specifically designed to enable diverse categories of authors to create high-quality content efficiently and easily. The platform will be flexible enough to support specialized academic writing requirements, practical professional guides, advanced scientific research, and general awareness books, all within a unified work environment.',

        // Target Audience
        examples: 'Examples',
        needs: 'Needs',
        academicResearcher: 'Academic & Researcher (Persona A)',
        academicExamples: 'Authors of "Fundamentals of Nuclear Medical Physics" and "Research Horizons in Molecular Imaging"',
        academicNeeds: 'Needs powerful tools for reference and citation management, mathematical equation writing, systematic book structure organization, and collaboration tools with reviewers or co-authors.',

        healthProfessional: 'Health Professional & Practitioner (Persona B)',
        professionalExamples: 'Authors of "Practical Guide to Quality Assurance and Control" and "Modern Radiation Therapy Physics"',
        professionalNeeds: 'Focuses on practical and procedural content. Needs ready-made templates for protocols, checklists, easy insertion of high-precision tables and diagrams, with version tracking capabilities.',

        researchDeveloper: 'Research Developer (Persona C)',
        developerExamples: 'Authors of "Radiopharmaceuticals" and "Hybrid Imaging"',
        developerNeeds: 'Needs advanced features for research collaboration, direct linking with scientific databases (PubMed, Google Scholar), tools for complex data visualizations, and ability to link content with external supplementary materials.',

        sciencePopularizer: 'Science Popularizer (Persona D)',
        popularizerExamples: 'Authors of "Nuclear Medicine: How Atoms Save Human Lives" and "Journey Inside the Human Body"',
        popularizerNeeds: 'Needs a simple, distraction-free writing interface, readability analysis tools, easy integration of attractive images and illustrations, flexible templates for narrative storytelling, and easy export to common e-book formats.',

        // Core Features
        generalFeatures: 'General Features (Available to All Users)',
        smartTextEditor: 'Smart Text Editor',
        smartTextEditorDesc: 'Simple yet powerful editor with full RTL support and advanced formatting options',
        structuralOrganization: 'Structural Organization',
        structuralOrganizationDesc: 'System for dividing books into chapters, sections, and scenes with easy drag-and-drop reordering',
        autoCloudSave: 'Automatic Cloud Save',
        autoCloudSaveDesc: 'Every change is saved instantly to the cloud to ensure no work is lost',
        goalTracking: 'Goal Setting & Tracking',
        goalTrackingDesc: 'Set daily or weekly word count goals and track progress',
        multipleExports: 'Multiple Export Options',
        multipleExportsDesc: 'Export manuscripts to various formats (DOCX, PDF, EPUB, MOBI) ready for publishing',
        realTimeCollaboration: 'Real-time Collaboration',
        realTimeCollaborationDesc: 'Invite editors or co-authors to work on the same document in real-time with change tracking and comments',

        academicWritingModule: 'Academic & Research Writing Module',
        referenceManager: 'Integrated Reference Manager',
        referenceManagerDesc: 'Tool for adding and managing references easily, with automatic citation and bibliography formatting',
        equationEditor: 'Equation Editor',
        equationEditorDesc: 'Advanced support for writing and formatting mathematical and physical equations',
        reviewTools: 'Review & Proofreading Tools',
        reviewToolsDesc: 'System enabling reviewers to add comments and suggestions in an organized manner',
        plagiarismChecker: 'Plagiarism Checker',
        plagiarismCheckerDesc: 'Integrated tool for checking content originality',

        professionalGuidesModule: 'Professional Guides Module',
        templateLibrary: 'Template Library',
        templateLibraryDesc: 'Ready-made templates for instructional guides, work protocols, and quality reports',
        diagramTools: 'Diagram Creation Tools',
        diagramToolsDesc: 'Tool for creating charts, flowcharts, and complex tables easily',
        versionControl: 'Version Control System',
        versionControlDesc: 'Track changes to important documents and revert to previous versions',

        publicBooksModule: 'Public Books Module',
        readabilityAnalyzer: 'Readability Analyzer',
        readabilityAnalyzerDesc: 'Tool for evaluating text simplicity and providing improvement suggestions',
        multimediaIntegration: 'Multimedia Integration',
        multimediaIntegrationDesc: 'Easy drag-and-drop interface for adding images, illustrations, and photo galleries',
        attractiveTemplates: 'Attractive Design Templates',
        attractiveTemplatesDesc: 'Ready-made templates for e-books and print books focusing on visual appeal',

        // User Experience
        simplicityAndFocus: 'Simplicity & Focus',
        simplicityAndFocusDesc: 'Clean and modern interface with distraction-free writing mode that hides all unnecessary menus',
        customization: 'Customization',
        customizationDesc: 'Allow users to customize workspace, including dark mode and font changes',
        fullArabicSupport: 'Full Arabic Support',
        fullArabicSupportDesc: 'Interface and features designed primarily to support right-to-left (RTL) text direction without any issues',

        // Technical Considerations
        cloudNativeArchitecture: 'Cloud-Native Architecture',
        cloudNativeArchitectureDesc: 'Application built on cloud infrastructure to ensure high availability and scalability',
        securityAndPrivacy: 'Security & Privacy',
        securityAndPrivacyDesc: 'Ensure protection of authors\' intellectual property through encryption and secure backup',
        integrations: 'Integrations',
        integrationsDesc: 'Ability to integrate with third-party services like Google Drive, Dropbox for storage, and Grammarly for proofreading',

        // Business Model
        freePlan: 'Free Plan',
        freePrice: 'Free',
        freePlanFeatures: 'Access to basic features for one book project\nLimited export options\nBasic templates\nCommunity support',

        writerPlan: 'Writer Plan',
        writerPrice: '$9.99/month',
        writerPlanFeatures: 'Unlimited projects\nAll general features\nOne specialized module of choice\nPriority support\nAdvanced export options',

        professionalPlan: 'Professional Plan',
        professionalPrice: '$19.99/month',
        professionalPlanFeatures: 'Access to all specialized modules\nAdvanced collaboration features\nDedicated technical support\nCustom templates\nAPI access\nWhite-label options'
    },
    ar: {
        // App Title and Hero
        appName: 'بايوميدليب',
        platformTitle: 'كتب الهندسة الطبية',
        heroTitle: 'بايوميدليب - منصة الكتب الإلكترونية للهندسة الطبية الحيوية',
        heroSubtitle: 'أنشئ واكتب وحرر وانشر كتب الهندسة الطبية الحيوية المهنية بميزات متقدمة',
        getStarted: 'ابدأ الآن',
        exploreBooks: 'استكشف الكتب',

        // Navigation
        home: 'الرئيسية',
        library: 'المكتبة',
        discovery: 'الاستكشاف',
        editor: 'المحرر',
        reader: 'القارئ',
        settings: 'الإعدادات',

        // Language
        language: 'اللغة',
        english: 'English',
        arabic: 'العربية',

        // Common
        save: 'حفظ',
        saving: 'جاري الحفظ...',
        cancel: 'إلغاء',
        delete: 'حذف',
        edit: 'تحرير',
        create: 'إنشاء',
        read: 'قراءة',
        publish: 'نشر',
        unpublish: 'إلغاء النشر',
        yes: 'نعم',
        no: 'لا',
        loading: 'جاري التحميل...',
        search: 'بحث',
        filter: 'تصفية',
        sort: 'ترتيب',
        title: 'العنوان',
        description: 'الوصف',
        author: 'المؤلف',
        category: 'الفئة',
        status: 'الحالة',
        date: 'التاريخ',
        preview: 'معاينة',
        download: 'تحميل',
        upload: 'رفع',
        close: 'إغلاق',

        // Library
        libraryTitle: 'مكتبتي',
        createNewBook: 'إنشاء كتاب جديد',
        bookTitle: 'عنوان الكتاب',
        bookDescription: 'وصف الكتاب',
        selectCategory: 'اختر الفئة',
        emptyLibraryTitle: 'مكتبتك فارغة',
        emptyLibrarySubtitle: 'ابدأ بإنشاء كتابك الأول لبناء مجموعتك',

        // Editor
        editorTitle: 'المحرر',
        backToLibrary: 'العودة للمكتبة',
        toolbar: 'شريط الأدوات',
        bold: 'عريض',
        italic: 'مائل',
        underline: 'تحته خط',
        strikethrough: 'يتوسطه خط',
        alignLeft: 'محاذاة يسار',
        alignCenter: 'محاذاة وسط',
        alignRight: 'محاذاة يمين',
        alignJustify: 'ضبط',
        insertLink: 'إدراج رابط',
        insertImage: 'إدراج صورة',
        insertTable: 'إدراج جدول',
        bulletList: 'قائمة نقطية',
        numberedList: 'قائمة مرقمة',
        fontSize: 'حجم الخط',
        fontFamily: 'نوع الخط',
        textColor: 'لون النص',
        backgroundColor: 'لون الخلفية',

        // Discovery
        discoveryTitle: 'استكشاف الكتب',
        searchBooks: 'البحث في الكتب...',
        allCategories: 'جميع الفئات',
        sortBy: 'ترتيب حسب:',
        newest: 'الأحدث',
        oldest: 'الأقدم',
        titleAZ: 'العنوان أ-ي',
        titleZA: 'العنوان ي-أ',
        viewMode: 'وضع العرض',
        gridView: 'عرض شبكي',
        listView: 'عرض قائمة',
        noBooks: 'لم يتم العثور على كتب',
        noBooksSub: 'جرب تعديل البحث أو المرشحات',
        by: 'بواسطة',

        // Home Page
        totalBooks: 'إجمالي الكتب',
        published: 'منشور',
        personalLibrary: 'المكتبة الشخصية',
        availableForReading: 'متاح للقراءة',
        richTextEditor: 'محرر النصوص المتقدم',
        richTextEditorDesc: 'محرر متقدم مع أدوات التنسيق وقوالب الهندسة الطبية الحيوية',
        bilingualSupport: 'الدعم ثنائي اللغة',
        bilingualSupportDesc: 'واجهة كاملة بالعربية والإنجليزية مع دعم الكتابة من اليمين لليسار',
        bookManagement: 'إدارة الكتب',
        bookManagementDesc: 'تنظيم وتصنيف وإدارة مجموعة كتبك',
        publishingSystem: 'نظام النشر',
        publishingSystemDesc: 'انشر وشارك كتبك مع المجتمع',
        responsiveDesign: 'التصميم المتجاوب',
        responsiveDesignDesc: 'يعمل بشكل مثالي على أجهزة سطح المكتب والأجهزة اللوحية والهواتف المحمولة',
        localStorage: 'التخزين المحلي',
        localStorageDesc: 'يتم تخزين بياناتك محلياً للخصوصية والوصول دون اتصال',
        advancedFeaturesTitle: 'الميزات المتقدمة',
        advancedFeaturesSubtitle: 'أدوات مهنية مصممة خصيصاً لمحتوى الهندسة الطبية الحيوية',
        recentBooksTitle: 'الكتب الحديثة',
        recentBooksSubtitle: 'استكشف أحدث الإضافات إلى مكتبتك',
        myLibrary: 'مكتبتي',
        readNow: 'اقرأ الآن',
        startWriting: 'ابدأ بكتابة محتواك هنا...',
        words: 'الكلمات',
        advancedEditor: 'المحرر المتقدم',

        // Reader
        readerTitle: 'القارئ',
        readingProgress: 'تقدم القراءة',
        backToDiscovery: 'العودة للاستكشاف',

        // File Upload
        uploadFile: 'رفع ملف',
        dragDropFile: 'اسحب وأفلت ملفاً هنا أو انقر للاختيار',
        selectFile: 'اختر ملف',
        uploading: 'جاري الرفع',
        supportedFormats: 'الصيغ المدعومة',
        importContent: 'استيراد المحتوى',

        // PDF Export
        advancedPDFExport: 'تصدير PDF متقدم',
        exportSettings: 'إعدادات التصدير',
        paperSize: 'حجم الورق',
        orientation: 'الاتجاه',
        portrait: 'عمودي',
        landscape: 'أفقي',
        margins: 'الهوامش',
        includeHeader: 'تضمين الرأس',
        includeFooter: 'تضمين التذييل',
        includePageNumbers: 'تضمين أرقام الصفحات',
        includeCoverPage: 'تضمين صفحة الغلاف',

        // Printer Interface
        printerInterface: 'واجهة الطابعة',
        availablePrinters: 'الطابعات المتاحة',
        printSettings: 'إعدادات الطباعة',
        copies: 'النسخ',
        colorMode: 'وضع الألوان',
        quality: 'الجودة',
        draft: 'مسودة',
        normal: 'عادي',
        high: 'عالي',

        // File Converter
        fileConverter: 'محول الملفات',
        selectFormat: 'اختر التنسيق',
        conversionSettings: 'إعدادات التحويل',
        includeStyles: 'تضمين الأنماط',
        includeImages: 'تضمين الصور',
        preserveFormatting: 'الحفاظ على التنسيق',
        embedFonts: 'تضمين الخطوط',
        compressImages: 'ضغط الصور',
        converting: 'جاري التحويل',
        convertAndDownload: 'تحويل وتنزيل',
        selectedFormat: 'التنسيق المختار',
        format: 'التنسيق',
        extension: 'الامتداد',
        pleaseSelectFormat: 'يرجى اختيار تنسيق',

        // Format Names
        htmlFormat: 'مستند HTML',
        markdownFormat: 'ماركداون',
        latexFormat: 'مستند LaTeX',
        rtfFormat: 'نص منسق',
        epubFormat: 'كتاب إلكتروني EPUB',
        docxFormat: 'مستند وورد',
        txtFormat: 'نص عادي',
        jsonFormat: 'بيانات JSON',

        // Format Descriptions
        htmlDescription: 'مستند متوافق مع الويب مع التنسيق',
        markdownDescription: 'ترميز خفيف للتوثيق',
        latexDescription: 'مستند أكاديمي مع الرموز الرياضية',
        rtfDescription: 'مستند نص منسق متعدد المنصات',
        epubDescription: 'تنسيق كتاب إلكتروني قياسي',
        docxDescription: 'مستند متوافق مع مايكروسوفت وورد',
        txtDescription: 'ملف نص عادي بسيط',
        jsonDescription: 'تنسيق بيانات منظم',

        // Additional Editor Features
        saved: 'محفوظ',
        editorLoading: 'جاري تحميل المحرر...',
        enterUrl: 'أدخل الرابط:',
        enterImageUrl: 'أدخل رابط الصورة:',
        image: 'صورة',
        clearFormatting: 'مسح التنسيق',
        decreaseFontSize: 'تصغير الخط',
        increaseFontSize: 'تكبير الخط',
        quote: 'اقتباس',

        // Templates and Equations
        templates: 'القوالب',
        equations: 'المعادلات',
        insertTemplate: 'إدراج قالب',
        insertEquation: 'إدراج معادلة',
        selectTemplateToPreview: 'اختر قالباً لمعاينته',
        selectEquationToPreview: 'اختر معادلة لمعاينتها',

        // Additional File Operations
        selectFileToPreview: 'اختر ملفاً لمعاينته',

        // Print and Export Additional
        priceHighLow: 'السعر: من الأعلى للأقل',
        priceLowHigh: 'السعر: من الأقل للأعلى',
        printQueue: 'قائمة انتظار الطباعة',
        noPrintJobs: 'لا توجد مهام طباعة في القائمة',
        color: 'ملون',
        blackAndWhite: 'أبيض وأسود',
        printerStatus: 'حالة الطابعة',
        ready: 'جاهز',
        offline: 'غير متصل',
        busy: 'مشغول',
        error: 'خطأ',
        paperJam: 'انحشار ورق',
        outOfPaper: 'نفاد الورق',
        outOfInk: 'نفاد الحبر',
        printerType: 'نوع الطابعة',
        inkjet: 'نفث حبر',
        laser: 'ليزر',
        thermal: 'حراري',
        colorSupport: 'دعم الألوان',
        duplexSupport: 'دعم الطباعة المزدوجة',
        pleaseSelectPrinter: 'يرجى اختيار طابعة',
        printerNotReady: 'الطابعة غير جاهزة',
        printJob: 'مهمة طباعة',
        queued: 'في الانتظار',
        printing: 'جاري الطباعة',
        completed: 'مكتمل',

        // Additional Status and States
        publishedStatus: 'منشور',
        lastUpdated: 'آخر تحديث',
        noBooksMessage: 'ابدأ بإنشاء كتابك الأول لبناء مكتبتك.',
        averageWords: 'متوسط الكلمات',
        totalReadership: 'إجمالي القراء',
        ascending: 'تصاعدي',
        descending: 'تنازلي',
        books: 'كتاب',
        createBookTitle: 'إنشاء كتاب جديد',
        bookTitleLabel: 'عنوان الكتاب',
        authorNameLabel: 'اسم المؤلف',
        descriptionLabel: 'الوصف',
        keywordsLabel: 'الكلمات المفتاحية (مفصولة بفواصل)',
        loadingLibrary: 'جاري تحميل المكتبة...',

        // Reader Additional
        lightTheme: 'فاتح',
        darkTheme: 'داكن',
        sepiaTheme: 'بني',
        minutesRead: 'دقيقة قراءة',
        readerLoading: 'جاري التحميل...',

        // Delete Confirmation
        confirmDelete: 'هل أنت متأكد من حذف هذا الكتاب؟',
        deleteConfirmation: 'لا يمكن التراجع عن هذا الإجراء.',

        // Additional Sorting and Filtering
        allStatuses: 'جميع الحالات',
        wordCount: 'عدد الكلمات',
        wordCountHigh: 'عدد الكلمات (عالي)',
        wordCountLow: 'عدد الكلمات (منخفض)',

        // Author Info
        authorInfo: 'د. محمد يعقوب إسماعيل',
        authorAffiliation: 'جامعة السودان للعلوم والتكنولوجيا - الهندسة الطبية الحيوية',
        copyright: '© 2025 جميع الحقوق محفوظة',
        contactEmail: '<EMAIL>',
        phoneNumbers: 'الهاتف: +249912867327, +966538076790',

        // Categories
        [BookCategory.BIOLOGICAL]: 'علوم بيولوجية',
        [BookCategory.FUNDAMENTAL_ENGINEERING]: 'علوم هندسية أساسية',
        [BookCategory.BIOMEDICAL_ENGINEERING]: 'الهندسة الطبية الحيوية',
        [BookCategory.CLINICAL_ENGINEERING]: 'الهندسة السريرية',
        [BookCategory.MEDICAL_DEVICES]: 'الأجهزة الطبية',
        [BookCategory.BIOMATERIALS]: 'المواد الحيوية',
        [BookCategory.BIOINFORMATICS]: 'المعلوماتية الحيوية',
        [BookCategory.REHABILITATION]: 'هندسة التأهيل',
        [BookCategory.RESEARCH_METHODS]: 'طرق البحث',

        // Project Orion
        projectOrionTitle: 'مشروع أوريون - تطبيق ويب متكامل لتأليف ونشر الكتب',
        projectOrionSubtitle: 'منصة شاملة مصممة خصيصاً لإنشاء محتوى الهندسة الطبية الحيوية',
        backToHome: 'العودة للرئيسية',

        // Navigation Sections
        visionAndGoal: 'الرؤية والهدف',
        targetAudience: 'الجمهور المستهدف',
        coreFeatures: 'الميزات الأساسية',
        userExperience: 'تجربة المستخدم',
        technicalConsiderations: 'الاعتبارات التقنية',
        businessModel: 'نموذج الأعمال',

        // Vision Section
        visionDescription: 'يهدف المشروع إلى تطوير تطبيق ويب شامل ومتكامل يُعنى بتأليف الكتب، مصمم خصيصًا لتمكين فئات متنوعة من المؤلفين من إنشاء محتوى عالي الجودة بكفاءة وسهولة. المنصة ستكون مرنة بما يكفي لدعم متطلبات الكتابة الأكاديمية المتخصصة، والأدلة المهنية العملية، والأبحاث العلمية المتقدمة، وكتب التوعية العامة، كل ذلك ضمن بيئة عمل واحدة وموحدة.',

        // Target Audience
        examples: 'أمثلة',
        needs: 'الاحتياجات',
        academicResearcher: 'الأكاديمي والباحث (الشخصية أ)',
        academicExamples: 'مؤلفو "أساسيات الفيزياء الطبية النووية" و "آفاق بحثية في التصوير الجزيئي"',
        academicNeeds: 'يحتاج إلى أدوات قوية لإدارة المراجع والاقتباسات، وكتابة المعادلات الرياضية والرموز العلمية، وتنظيم هيكل الكتاب بشكل منهجي، بالإضافة إلى أدوات للتعاون مع مراجعين أو مؤلفين مشاركين.',

        healthProfessional: 'المهني والممارس الصحي (الشخصية ب)',
        professionalExamples: 'مؤلفو "الدليل العملي لضمان ومراقبة الجودة" و "فيزياء العلاج الإشعاعي الحديث"',
        professionalNeeds: 'يركز على المحتوى العملي والإجرائي. يحتاج إلى قوالب جاهزة للبروتوكولات، وقوائم المراجعة، وسهولة في إدراج جداول ورسوم بيانية توضيحية عالية الدقة، مع إمكانية تتبع الإصدارات المختلفة.',

        researchDeveloper: 'الباحث المطور (الشخصية ج)',
        developerExamples: 'مؤلفو "المستحضرات الصيدلانية الإشعاعية" و "التصوير الهجين"',
        developerNeeds: 'يحتاج إلى ميزات متقدمة للتعاون البحثي، وربط مباشر مع قواعد البيانات العلمية، وأدوات لتضمين تصورات البيانات المعقدة، والقدرة على ربط المحتوى بمواد تكميلية خارجية.',

        sciencePopularizer: 'المؤلف لغير المتخصصين (الشخصية د)',
        popularizerExamples: 'مؤلفو "الطب النووي: كيف تُنقذ الذرة حياة الإنسان" و "رحلة داخل جسم الإنسان"',
        popularizerNeeds: 'يحتاج إلى واجهة كتابة بسيطة وخالية من المشتتات، وأدوات لتحليل قابلية القراءة، وسهولة في دمج الصور والرسوم التوضيحية الجذابة، وقوالب مرنة تناسب السرد القصصي.',

        // Core Features
        generalFeatures: 'الميزات العامة (متوفرة لجميع المستخدمين)',
        smartTextEditor: 'محرر نصوص ذكي',
        smartTextEditorDesc: 'محرر بسيط وقوي يدعم الكتابة من اليمين إلى اليسار مع خيارات تنسيق متقدمة',
        structuralOrganization: 'التنظيم الهيكلي',
        structuralOrganizationDesc: 'نظام لتقسيم الكتاب إلى فصول وأقسام مع سهولة إعادة الترتيب بالسحب والإفلات',
        autoCloudSave: 'الحفظ السحابي التلقائي',
        autoCloudSaveDesc: 'يتم حفظ كل تغيير بشكل فوري على السحابة لضمان عدم فقدان أي عمل',
        goalTracking: 'تحديد الأهداف والمتابعة',
        goalTrackingDesc: 'تحديد أهداف يومية أو أسبوعية لعدد الكلمات وتتبع التقدم',
        multipleExports: 'خيارات التصدير المتعددة',
        multipleExportsDesc: 'تصدير المخطوطة إلى صيغ متنوعة جاهزة للنشر',
        realTimeCollaboration: 'التعاون المباشر',
        realTimeCollaborationDesc: 'دعوة محررين أو مؤلفين مشاركين للعمل على نفس المستند في الوقت الفعلي',

        academicWritingModule: 'وحدة الكتابة الأكاديمية والبحثية',
        referenceManager: 'مدير مراجع مدمج',
        referenceManagerDesc: 'أداة لإضافة وإدارة المراجع بسهولة مع تنسيق الاقتباسات تلقائياً',
        equationEditor: 'محرر المعادلات',
        equationEditorDesc: 'دعم متقدم لكتابة وتنسيق المعادلات الرياضية والفيزيائية',
        reviewTools: 'أدوات المراجعة والتدقيق',
        reviewToolsDesc: 'نظام لتمكين المراجعين من إضافة تعليقات واقتراحات بشكل منظم',
        plagiarismChecker: 'فاحص الانتحال',
        plagiarismCheckerDesc: 'أداة مدمجة للتحقق من أصالة المحتوى',

        professionalGuidesModule: 'وحدة الأدلة المهنية',
        templateLibrary: 'مكتبة قوالب',
        templateLibraryDesc: 'قوالب جاهزة للأدلة الإرشادية وبروتوكولات العمل وتقارير الجودة',
        diagramTools: 'أدوات إنشاء المخططات',
        diagramToolsDesc: 'أداة لإنشاء رسوم بيانية وخرائط تدفق وجداول معقدة بسهولة',
        versionControl: 'نظام التحكم في الإصدارات',
        versionControlDesc: 'تتبع التعديلات على المستندات المهمة والعودة إلى إصدارات سابقة',

        publicBooksModule: 'وحدة كتب الجمهور العام',
        readabilityAnalyzer: 'محلل قابلية القراءة',
        readabilityAnalyzerDesc: 'أداة لتقييم بساطة النص وتقديم اقتراحات لتحسينه',
        multimediaIntegration: 'تكامل الوسائط المتعددة',
        multimediaIntegrationDesc: 'واجهة سهلة للسحب والإفلات لإضافة الصور والرسوم التوضيحية',
        attractiveTemplates: 'قوالب تصميم جذابة',
        attractiveTemplatesDesc: 'قوالب جاهزة للكتب الإلكترونية والمطبوعة تركز على الجاذبية البصرية',

        // User Experience
        simplicityAndFocus: 'البساطة والتركيز',
        simplicityAndFocusDesc: 'واجهة نظيفة وحديثة مع وضع الكتابة الخالية من المشتتات',
        customization: 'التخصيص',
        customizationDesc: 'السماح للمستخدم بتخصيص مساحة العمل مثل اختيار الوضع الليلي وتغيير الخطوط',
        fullArabicSupport: 'دعم كامل للغة العربية',
        fullArabicSupportDesc: 'الواجهة والميزات مصممة بشكل أساسي لدعم اتجاه النص من اليمين إلى اليسار دون أي مشاكل',

        // Technical Considerations
        cloudNativeArchitecture: 'بنية سحابية',
        cloudNativeArchitectureDesc: 'بناء التطبيق على بنية تحتية سحابية لضمان التوافر العالي وقابلية التوسع',
        securityAndPrivacy: 'الأمان والخصوصية',
        securityAndPrivacyDesc: 'ضمان حماية حقوق الملكية الفكرية للمؤلفين من خلال التشفير والنسخ الاحتياطي الآمن',
        integrations: 'التكامل',
        integrationsDesc: 'إمكانية التكامل مع خدمات طرف ثالث مثل Google Drive وDropbox للتخزين وGrammarly للتدقيق اللغوي',

        // Business Model
        freePlan: 'الخطة المجانية',
        freePrice: 'مجاني',
        freePlanFeatures: 'الوصول إلى الميزات الأساسية لمشروع كتاب واحد\nخيارات تصدير محدودة\nقوالب أساسية\nدعم المجتمع',

        writerPlan: 'خطة الكاتب',
        writerPrice: '9.99$ شهرياً',
        writerPlanFeatures: 'مشاريع غير محدودة\nجميع الميزات العامة\nوحدة متخصصة واحدة من الاختيار\nدعم ذو أولوية\nخيارات تصدير متقدمة',

        professionalPlan: 'خطة المحترفين',
        professionalPrice: '19.99$ شهرياً',
        professionalPlanFeatures: 'الوصول إلى جميع الوحدات المتخصصة\nميزات تعاون متقدمة\nدعم فني مخصص\nقوالب مخصصة\nوصول API\nخيارات العلامة البيضاء'
    }
};

interface LanguageContextType {
    language: 'en' | 'ar';
    changeLanguage: (lang: 'en' | 'ar') => void;
    t: (key: string) => string;
    dir: 'ltr' | 'rtl';
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
    const context = useContext(LanguageContext);
    if (!context) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
};

interface LanguageProviderProps {
    children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
    const [language, setLanguage] = useState<'en' | 'ar'>(() => {
        const saved = localStorage.getItem('language');
        return (saved as 'en' | 'ar') || 'en';
    });

    const changeLanguage = (lang: 'en' | 'ar') => {
        setLanguage(lang);
        localStorage.setItem('language', lang);
        document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
        document.documentElement.lang = lang;
    };

    const t = (key: string): string => {
        return translations[language][key as keyof typeof translations['en']] || key;
    };

    const dir = language === 'ar' ? 'rtl' : 'ltr';

    useEffect(() => {
        document.documentElement.dir = dir;
        document.documentElement.lang = language;
    }, [language, dir]);

    const value: LanguageContextType = {
        language,
        changeLanguage,
        t,
        dir
    };

    return React.createElement(
        LanguageContext.Provider,
        { value },
        children
    );
};
