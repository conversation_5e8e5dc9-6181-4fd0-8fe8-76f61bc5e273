
export enum BookCategory {
  BIOLOGICAL = 'Biological',
  FUNDAMENTAL_ENGINEERING = 'Fundamental Engineering Sciences',
  ELECTRONICS = 'Electronics',
  MEDICAL_DEVICES = 'Medical Devices',
  SENSORS_TRANSDUCERS = 'Sensors & Transducers',
  OTHER = 'Other',
}

export interface Book {
  id: string;
  title: string;
  author: string;
  description: string;
  category: BookCategory;
  content: string;
  keywords: string[];
  isPublished: boolean;
  price: number | 'free';
  createdAt: string;
  updatedAt: string;
  wordCount: number;
}
