
import React, { useState } from 'react';
import { useBooks } from '../../hooks/useBooks';
import { Book, BookCategory } from '../../types';
import { useNavigate } from 'react-router-dom';
import Modal from '../Modal';
import { useLanguage } from '../../i18n';

interface BookCardProps {
    book: Book;
    onEdit: (id: string) => void;
    onDelete: (id: string) => void;
    onRead: (id: string) => void;
}

const BookCard = ({ book, onEdit, onDelete, onRead }: BookCardProps) => {
    const { t } = useLanguage();
    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow p-4 flex flex-col justify-between">
            <div>
                <h3 className="text-lg font-bold text-blue-600 dark:text-blue-400 truncate">{book.title}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{t('by')} {book.author}</p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">{t(book.category)}</p>
                <p className="text-sm mt-2 text-gray-700 dark:text-gray-300 line-clamp-2">{book.description}</p>
                <div className="text-xs mt-2 text-gray-500 dark:text-gray-400">
                    <span>{t('words')}: {book.wordCount}</span>
                    <span className="mx-2">|</span>
                    <span>{book.isPublished ? t('publishedStatus') : t('draft')}</span>
                </div>
            </div>
            <div className="flex justify-end space-x-2 mt-4">
                <button onClick={() => onRead(book.id)} className="text-green-500 hover:text-green-700 dark:hover:text-green-400 p-2 rounded-full transition"><i className="fas fa-book-open"></i></button>
                <button onClick={() => onEdit(book.id)} className="text-blue-500 hover:text-blue-700 dark:hover:text-blue-400 p-2 rounded-full transition"><i className="fas fa-pencil-alt"></i></button>
                <button onClick={() => onDelete(book.id)} className="text-red-500 hover:text-red-700 dark:hover:text-red-400 p-2 rounded-full transition"><i className="fas fa-trash"></i></button>
            </div>
        </div>
    )
};

const Library = () => {
    const { books, addBook, deleteBook, loading } = useBooks();
    const { t, dir } = useLanguage();
    const navigate = useNavigate();
    const [isCreateModalOpen, setCreateModalOpen] = useState(false);
    const [newBookData, setNewBookData] = useState({ title: '', author: '', description: '', category: BookCategory.OTHER, keywords: '' });
    
    const totalWords = books.reduce((sum, book) => sum + book.wordCount, 0);

    const handleCreateBook = () => {
        if (newBookData.title && newBookData.author) {
            const newBook = addBook({
                title: newBookData.title,
                author: newBookData.author,
                description: newBookData.description,
                category: newBookData.category,
                keywords: newBookData.keywords.split(',').map(k => k.trim()).filter(Boolean),
            });
            setCreateModalOpen(false);
            setNewBookData({ title: '', author: '', description: '', category: BookCategory.OTHER, keywords: '' });
            navigate(`/editor/${newBook.id}`);
        }
    };
    
    const handleDeleteBook = (id: string) => {
        if(window.confirm(t('deleteConfirmation'))){
            deleteBook(id);
        }
    }

    if (loading) {
        return <div className="text-center p-10">Loading library...</div>;
    }

    return (
        <div className="container mx-auto">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-3xl font-bold text-gray-800 dark:text-white">{t('libraryTitle')}</h2>
                <button onClick={() => setCreateModalOpen(true)} className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg shadow-lg transition-transform transform hover:scale-105 flex items-center">
                    <i className={`fas fa-plus ${dir === 'rtl' ? 'ms-2' : 'me-2'}`}></i>{t('createNewBook')}
                </button>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md text-center">
                    <p className="text-4xl font-bold text-blue-500">{books.length}</p>
                    <p className="text-gray-600 dark:text-gray-400">{t('totalBooks')}</p>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md text-center">
                    <p className="text-4xl font-bold text-green-500">{books.filter(b => b.isPublished).length}</p>
                    <p className="text-gray-600 dark:text-gray-400">{t('published')}</p>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md text-center">
                    <p className="text-4xl font-bold text-yellow-500">{totalWords.toLocaleString()}</p>
                    <p className="text-gray-600 dark:text-gray-400">{t('totalWords')}</p>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md text-center">
                    <p className="text-4xl font-bold text-purple-500">0</p>
                    <p className="text-gray-600 dark:text-gray-400">{t('totalReadership')}</p>
                </div>
            </div>

            {books.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {books.map(book => (
                        <BookCard 
                            key={book.id} 
                            book={book} 
                            onEdit={(id) => navigate(`/editor/${id}`)}
                            onDelete={handleDeleteBook}
                            onRead={(id) => navigate(`/read/${id}`)}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-16 bg-white dark:bg-gray-800 rounded-lg shadow-md">
                    <i className="fas fa-box-open text-6xl text-gray-400 dark:text-gray-500"></i>
                    <h3 className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">{t('emptyLibraryTitle')}</h3>
                    <p className="mt-2 text-gray-500 dark:text-gray-400">{t('emptyLibrarySubtitle')}</p>
                </div>
            )}

            <Modal isOpen={isCreateModalOpen} onClose={() => setCreateModalOpen(false)} title={t('createBookTitle')}>
                <div className="space-y-4">
                    <input type="text" placeholder={t('bookTitleLabel')} value={newBookData.title} onChange={(e) => setNewBookData({...newBookData, title: e.target.value})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600" />
                    <input type="text" placeholder={t('authorNameLabel')} value={newBookData.author} onChange={(e) => setNewBookData({...newBookData, author: e.target.value})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600" />
                    <textarea placeholder={t('descriptionLabel')} value={newBookData.description} onChange={(e) => setNewBookData({...newBookData, description: e.target.value})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600" rows={3}></textarea>
                    <select value={newBookData.category} onChange={(e) => setNewBookData({...newBookData, category: e.target.value as BookCategory})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                        {Object.values(BookCategory).map(cat => <option key={cat} value={cat}>{t(cat)}</option>)}
                    </select>
                    <input type="text" placeholder={t('keywordsLabel')} value={newBookData.keywords} onChange={(e) => setNewBookData({...newBookData, keywords: e.target.value})} className="w-full p-2 border rounded bg-gray-50 dark:bg-gray-700 dark:border-gray-600" />
                    <div className="flex justify-end space-x-2">
                        <button onClick={() => setCreateModalOpen(false)} className="px-4 py-2 rounded text-gray-600 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500">{t('cancel')}</button>
                        <button onClick={handleCreateBook} className="px-4 py-2 rounded text-white bg-blue-500 hover:bg-blue-600">{t('create')}</button>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default Library;