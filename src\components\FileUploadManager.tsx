import React, { useState, useRef } from 'react';
import { useLanguage } from '../i18n';

interface FileUploadManagerProps {
    onImportContent: (content: string) => void;
    onClose: () => void;
}

const FileUploadManager: React.FC<FileUploadManagerProps> = ({ onImportContent, onClose }) => {
    const { t, language } = useLanguage();
    const [dragActive, setDragActive] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [previewContent, setPreviewContent] = useState<string>('');
    const [fileName, setFileName] = useState<string>('');
    const fileInputRef = useRef<HTMLInputElement>(null);

    const supportedFormats = [
        { extension: '.txt', description: 'Plain Text', descriptionAr: 'نص عادي', icon: 'fas fa-file-alt' },
        { extension: '.html', description: 'HTML Document', descriptionAr: 'مستند HTML', icon: 'fas fa-code' },
        { extension: '.md', description: 'Markdown', descriptionAr: 'ماركداون', icon: 'fab fa-markdown' },
        { extension: '.docx', description: 'Word Document', descriptionAr: 'مستند وورد', icon: 'fas fa-file-word' },
        { extension: '.pdf', description: 'PDF Document', descriptionAr: 'مستند PDF', icon: 'fas fa-file-pdf' },
        { extension: '.rtf', description: 'Rich Text Format', descriptionAr: 'نص منسق', icon: 'fas fa-file-alt' }
    ];

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);
        
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFile(e.dataTransfer.files[0]);
        }
    };

    const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFile(e.target.files[0]);
        }
    };

    const handleFile = async (file: File) => {
        setUploading(true);
        setUploadProgress(0);
        setFileName(file.name);

        try {
            const content = await parseFile(file);
            setPreviewContent(content);
            
            // Simulate upload progress
            for (let i = 0; i <= 100; i += 10) {
                setUploadProgress(i);
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        } catch (error) {
            console.error('Error parsing file:', error);
            alert(t('fileParsingError'));
        } finally {
            setUploading(false);
        }
    };

    const parseFile = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            const extension = file.name.toLowerCase().split('.').pop();

            reader.onload = (e) => {
                const result = e.target?.result as string;
                
                try {
                    switch (extension) {
                        case 'txt':
                            resolve(result.replace(/\n/g, '<br>'));
                            break;
                        case 'html':
                            resolve(result);
                            break;
                        case 'md':
                            resolve(parseMarkdown(result));
                            break;
                        case 'rtf':
                            resolve(parseRTF(result));
                            break;
                        default:
                            resolve(result.replace(/\n/g, '<br>'));
                    }
                } catch (error) {
                    reject(error);
                }
            };

            reader.onerror = () => reject(new Error('File reading failed'));
            
            if (extension === 'pdf') {
                // For PDF files, we'll need a PDF parsing library
                // For now, just show a message
                resolve('<p>PDF parsing requires additional setup. Please convert to text format first.</p>');
            } else {
                reader.readAsText(file);
            }
        });
    };

    const parseMarkdown = (markdown: string): string => {
        // Simple markdown to HTML conversion
        return markdown
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
            .replace(/\*(.*)\*/gim, '<em>$1</em>')
            .replace(/\n/gim, '<br>');
    };

    const parseRTF = (rtf: string): string => {
        // Simple RTF to HTML conversion (basic implementation)
        return rtf
            .replace(/\\par\s?/g, '<br>')
            .replace(/\{\\b\s?(.*?)\}/g, '<strong>$1</strong>')
            .replace(/\{\\i\s?(.*?)\}/g, '<em>$1</em>')
            .replace(/\\[a-z]+\d*\s?/g, '')
            .replace(/[{}]/g, '');
    };

    const handleImport = () => {
        if (previewContent) {
            onImportContent(previewContent);
            onClose();
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-semibold">{t('importFromFile')}</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Upload Area */}
                    <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-3">{t('uploadFile')}</h4>
                        
                        {/* Drag and Drop Area */}
                        <div
                            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                                dragActive
                                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                                    : 'border-gray-300 dark:border-gray-600'
                            }`}
                            onDragEnter={handleDrag}
                            onDragLeave={handleDrag}
                            onDragOver={handleDrag}
                            onDrop={handleDrop}
                        >
                            <i className="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                {t('dragDropFile')}
                            </p>
                            <button
                                onClick={() => fileInputRef.current?.click()}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
                            >
                                {t('selectFile')}
                            </button>
                            <input
                                ref={fileInputRef}
                                type="file"
                                onChange={handleFileInput}
                                accept=".txt,.html,.md,.docx,.pdf,.rtf"
                                className="hidden"
                            />
                        </div>

                        {/* Upload Progress */}
                        {uploading && (
                            <div className="mt-4">
                                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                    <span>{t('uploading')}: {fileName}</span>
                                    <span>{uploadProgress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div
                                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${uploadProgress}%` }}
                                    ></div>
                                </div>
                            </div>
                        )}

                        {/* Supported Formats */}
                        <div className="mt-6">
                            <h5 className="font-medium text-gray-900 dark:text-white mb-3">{t('supportedFormats')}</h5>
                            <div className="grid grid-cols-2 gap-2">
                                {supportedFormats.map((format, index) => (
                                    <div key={index} className="flex items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                                        <i className={`${format.icon} text-gray-500 mr-2`}></i>
                                        <div>
                                            <div className="text-sm font-medium">{format.extension}</div>
                                            <div className="text-xs text-gray-600 dark:text-gray-400">
                                                {language === 'ar' ? format.descriptionAr : format.description}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Preview Area */}
                    <div>
                        <div className="flex justify-between items-center mb-3">
                            <h4 className="font-medium text-gray-900 dark:text-white">{t('preview')}</h4>
                            {previewContent && (
                                <button
                                    onClick={handleImport}
                                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
                                >
                                    {t('importContent')}
                                </button>
                            )}
                        </div>
                        
                        <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-700 min-h-96 max-h-96 overflow-y-auto">
                            {previewContent ? (
                                <div 
                                    className="prose dark:prose-invert max-w-none"
                                    dangerouslySetInnerHTML={{ __html: previewContent }}
                                />
                            ) : (
                                <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                                    <div className="text-center">
                                        <i className="fas fa-file-import text-4xl mb-4"></i>
                                        <p>{t('selectFileToPreview')}</p>
                                    </div>
                                </div>
                            )}
                        </div>

                        {fileName && (
                            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
                                <div className="flex items-center">
                                    <i className="fas fa-info-circle text-blue-500 mr-2"></i>
                                    <div>
                                        <div className="text-sm font-medium text-blue-900 dark:text-blue-100">
                                            {t('fileName')}: {fileName}
                                        </div>
                                        <div className="text-xs text-blue-700 dark:text-blue-300">
                                            {t('contentParsedSuccessfully')}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FileUploadManager;
