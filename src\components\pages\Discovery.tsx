
import React, { useState, useMemo } from 'react';
import { useBooks } from '../../hooks/useBooks';
import { Book, BookCategory } from '../../types';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../i18n';

interface DiscoveryCardProps {
    book: Book;
    onRead: (id: string) => void;
    viewMode?: 'grid' | 'list';
}

const DiscoveryCard = ({ book, onRead, viewMode = 'grid' }: DiscoveryCardProps) => {
    const { t } = useLanguage();

    if (viewMode === 'list') {
        return (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 flex items-center gap-6">
                <div className="flex-shrink-0 w-16 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i className="fas fa-book text-white text-2xl"></i>
                </div>
                <div className="flex-grow">
                    <div className="flex items-start justify-between">
                        <div className="flex-grow">
                            <h3 className="text-xl font-bold text-blue-600 dark:text-blue-400 mb-1">{book.title}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{t('by')} {book.author}</p>
                            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400 mb-2">
                                <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">{t(book.category)}</span>
                                <span>{book.wordCount.toLocaleString()} {t('words')}</span>
                                <span>{new Date(book.updatedAt).toLocaleDateString('ar-SA')}</span>
                            </div>
                            <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">{book.description}</p>
                        </div>
                        <div className="flex flex-col items-end gap-2 ml-4">
                            <span className="text-lg font-semibold text-green-600 dark:text-green-400">
                                {book.price === 'free' ? t('free') : `$${book.price.toFixed(2)}`}
                            </span>
                            <button
                                onClick={() => onRead(book.id)}
                                className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md transition-all transform hover:scale-105 flex items-center gap-2"
                            >
                                <i className="fas fa-book-open"></i>
                                {t('read')}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-4 flex flex-col transform hover:scale-105">
            <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-book text-white"></i>
                </div>
                <div className="flex-grow min-w-0">
                    <h3 className="text-lg font-bold text-blue-600 dark:text-blue-400 truncate">{book.title}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{t('by')} {book.author}</p>
                </div>
            </div>
            <div className="flex items-center gap-2 mb-2">
                <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded">{t(book.category)}</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">{book.wordCount.toLocaleString()} {t('words')}</span>
            </div>
            <p className="text-sm mt-2 text-gray-700 dark:text-gray-300 flex-grow line-clamp-3">{book.description}</p>
            <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <span className="text-lg font-semibold text-green-600 dark:text-green-400">
                    {book.price === 'free' ? t('free') : `$${book.price.toFixed(2)}`}
                </span>
                <button
                    onClick={() => onRead(book.id)}
                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-3 rounded-md transition-all transform hover:scale-105 flex items-center gap-1"
                >
                    <i className="fas fa-book-open text-sm"></i>
                    {t('read')}
                </button>
            </div>
        </div>
    );
};

const Discovery = () => {
    const { books, loading } = useBooks();
    const { t } = useLanguage();
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<BookCategory | 'all'>('all');
    const [sortBy, setSortBy] = useState<'title' | 'author' | 'date' | 'wordCount'>('date');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    
    const publishedBooks = useMemo(() => books.filter(book => book.isPublished), [books]);

    const filteredBooks = useMemo(() => {
        return publishedBooks
            .filter(book => {
                const matchesCategory = selectedCategory === 'all' || book.category === selectedCategory;
                const matchesSearch = searchTerm === '' ||
                    book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    book.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    book.keywords.some(k => k.toLowerCase().includes(searchTerm.toLowerCase()));

                return matchesCategory && matchesSearch;
            })
            .sort((a, b) => {
                let comparison = 0;
                switch (sortBy) {
                    case 'title':
                        comparison = a.title.localeCompare(b.title);
                        break;
                    case 'author':
                        comparison = a.author.localeCompare(b.author);
                        break;
                    case 'date':
                        comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
                        break;
                    case 'wordCount':
                        comparison = a.wordCount - b.wordCount;
                        break;
                }
                return sortOrder === 'asc' ? comparison : -comparison;
            });
    }, [publishedBooks, searchTerm, selectedCategory, sortBy, sortOrder]);
    
    if (loading) {
        return <div className="text-center p-10">{t('readerLoading')}</div>;
    }

    return (
        <div className="container mx-auto">
            <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">{t('discoveryTitle')}</h2>
            
            <div className="flex flex-col md:flex-row gap-4 mb-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
                <div className="relative flex-grow">
                    <input
                        type="text"
                        placeholder={t('searchBooks')}
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                        className="w-full p-3 ps-10 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                    />
                    <i className="fas fa-search absolute start-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <select
                    value={selectedCategory}
                    onChange={e => setSelectedCategory(e.target.value as BookCategory | 'all')}
                    className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                >
                    <option value="all">{t('allCategories')}</option>
                    {Object.values(BookCategory).map(cat => <option key={cat} value={cat}>{t(cat)}</option>)}
                </select>
            </div>

            {/* Sort and View Controls */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('sortBy')}</label>
                        <select
                            value={sortBy}
                            onChange={(e) => setSortBy(e.target.value as 'title' | 'author' | 'date' | 'wordCount')}
                            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="date">{t('date')}</option>
                            <option value="title">{t('title')}</option>
                            <option value="author">{t('author')}</option>
                            <option value="wordCount">{t('wordCount')}</option>
                        </select>
                    </div>
                    <button
                        onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                        className="p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        title={sortOrder === 'asc' ? t('ascending') : t('descending')}
                    >
                        <i className={`fas fa-sort-${sortOrder === 'asc' ? 'up' : 'down'}`}></i>
                    </button>
                </div>

                <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                        {filteredBooks.length} {t('books')}
                    </span>
                    <div className="flex border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
                        <button
                            onClick={() => setViewMode('grid')}
                            className={`p-2 transition-colors ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700'}`}
                            title={t('gridView')}
                        >
                            <i className="fas fa-th"></i>
                        </button>
                        <button
                            onClick={() => setViewMode('list')}
                            className={`p-2 transition-colors ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700'}`}
                            title={t('listView')}
                        >
                            <i className="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            {filteredBooks.length > 0 ? (
                <div className={viewMode === 'grid'
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                    : "space-y-4"
                }>
                    {filteredBooks.map(book => (
                        <DiscoveryCard
                            key={book.id}
                            book={book}
                            onRead={(id) => navigate(`/read/${id}`)}
                            viewMode={viewMode}
                        />
                    ))}
                </div>
            ) : (
                <div className="text-center py-16 bg-white dark:bg-gray-800 rounded-lg shadow-md">
                    <i className="fas fa-search-minus text-6xl text-gray-400 dark:text-gray-500"></i>
                    <h3 className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">{t('noBooks')}</h3>
                    <p className="mt-2 text-gray-500 dark:text-gray-400">{t('noBooksSub')}</p>
                </div>
            )}
        </div>
    );
};

export default Discovery;